1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.postureapp.android"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:7:3-75
11-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:7:20-73
12    <uses-permission android:name="android.permission.CAMERA" />
12-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:2:3-62
12-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:2:20-60
13    <uses-permission android:name="android.permission.INTERNET" />
13-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:3:3-64
13-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:3:20-62
14    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
14-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:4:3-77
14-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:4:20-75
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:5:3-77
15-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:5:20-75
16    <uses-permission android:name="android.permission.RECORD_AUDIO" />
16-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:6:3-68
16-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:6:20-66
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:8:3-63
17-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:8:20-61
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:9:3-78
18-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:9:20-76
19
20    <queries>
20-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:10:3-16:13
21        <intent>
21-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:11:5-15:14
22            <action android:name="android.intent.action.VIEW" />
22-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:12:7-58
22-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:12:15-56
23
24            <category android:name="android.intent.category.BROWSABLE" />
24-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:13:7-67
24-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:13:17-65
25
26            <data android:scheme="https" />
26-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:7-37
26-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:13-35
27        </intent>
28        <!-- Query open documents -->
29        <intent>
29-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
30            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
30-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
30-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
31        </intent>
32        <intent>
32-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:11:9-17:18
33            <action android:name="android.intent.action.VIEW" />
33-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:12:7-58
33-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:12:15-56
34
35            <data
35-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:7-37
36                android:mimeType="*/*"
37                android:scheme="*" />
37-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:13-35
38        </intent>
39        <intent>
39-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:18:9-27:18
40            <action android:name="android.intent.action.VIEW" />
40-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:12:7-58
40-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:12:15-56
41
42            <category android:name="android.intent.category.BROWSABLE" />
42-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:13:7-67
42-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:13:17-65
43
44            <data
44-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:7-37
45                android:host="pay"
46                android:mimeType="*/*"
47                android:scheme="upi" />
47-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:13-35
48        </intent>
49        <intent>
49-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:28:9-30:18
50            <action android:name="android.intent.action.MAIN" />
50-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:24:9-60
50-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:24:17-58
51        </intent>
52        <intent>
52-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:31:9-35:18
53            <action android:name="android.intent.action.SEND" />
53-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:32:13-65
53-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:32:21-62
54
55            <data android:mimeType="*/*" />
55-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:7-37
56        </intent>
57        <intent>
57-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:36:9-38:18
58            <action android:name="rzp.device_token.share" />
58-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:37:13-61
58-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:37:21-58
59        </intent>
60        <intent>
60-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf8b5107ff437249fa41919294d3c2e3\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:23:9-25:18
61            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
61-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf8b5107ff437249fa41919294d3c2e3\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:24:13-86
61-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf8b5107ff437249fa41919294d3c2e3\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:24:21-83
62        </intent>
63    </queries>
64
65    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
65-->[:react-native-community_netinfo] D:\PostureApp\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
65-->[:react-native-community_netinfo] D:\PostureApp\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
66    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
66-->[:react-native-community_netinfo] D:\PostureApp\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
66-->[:react-native-community_netinfo] D:\PostureApp\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
67    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
67-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:7:5-81
67-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:7:22-78
68    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
68-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:8:5-77
68-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:8:22-74
69    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
69-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
69-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:22-65
70    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
70-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
70-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
71    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
71-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88b9f6ad7ed87d7d30486cdd2fcbb5f7\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
71-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88b9f6ad7ed87d7d30486cdd2fcbb5f7\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
72    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
72-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88b9f6ad7ed87d7d30486cdd2fcbb5f7\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
72-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88b9f6ad7ed87d7d30486cdd2fcbb5f7\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
73
74    <uses-feature
74-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29f3e55b4764c3def309e3d61ebb4352\transformed\play-services-maps-17.0.0\AndroidManifest.xml:26:5-28:35
75        android:glEsVersion="0x00020000"
75-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29f3e55b4764c3def309e3d61ebb4352\transformed\play-services-maps-17.0.0\AndroidManifest.xml:27:9-41
76        android:required="true" />
76-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29f3e55b4764c3def309e3d61ebb4352\transformed\play-services-maps-17.0.0\AndroidManifest.xml:28:9-32
77
78    <permission
78-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
79        android:name="com.postureapp.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
79-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
80        android:protectionLevel="signature" />
80-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
81
82    <uses-permission android:name="com.postureapp.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
82-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
82-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
83    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
83-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e75f83e5e2bd4ad05880a4e7cced326b\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
83-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e75f83e5e2bd4ad05880a4e7cced326b\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
84    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
85    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
86    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
87    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
88    <!-- for Samsung -->
89    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
90    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
91    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
92    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
93    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
94    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
95    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
96    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
96-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
96-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
97    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
97-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
97-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
98    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
98-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
98-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
99    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
99-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
99-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
100    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
100-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
100-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
101    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
101-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
101-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
102    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
102-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
102-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
103    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
103-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
103-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
104    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
104-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
104-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
105
106    <application
106-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:3-34:17
107        android:name="com.postureapp.android.MainApplication"
107-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:16-47
108        android:allowBackup="true"
108-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:162-188
109        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
109-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
110        android:dataExtractionRules="@xml/secure_store_data_extraction_rules"
110-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:307-376
111        android:debuggable="true"
112        android:extractNativeLibs="false"
113        android:fullBackupContent="@xml/secure_store_backup_rules"
113-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:248-306
114        android:icon="@mipmap/ic_launcher"
114-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:81-115
115        android:label="@string/app_name"
115-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:48-80
116        android:roundIcon="@mipmap/ic_launcher_round"
116-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:116-161
117        android:supportsRtl="true"
117-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:221-247
118        android:theme="@style/AppTheme"
118-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:189-220
119        android:usesCleartextTraffic="true" >
119-->D:\PostureApp\android\app\src\debug\AndroidManifest.xml:6:18-53
120        <meta-data
120-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:18:5-83
121            android:name="expo.modules.updates.ENABLED"
121-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:18:16-59
122            android:value="false" />
122-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:18:60-81
123        <meta-data
123-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:19:5-119
124            android:name="expo.modules.updates.EXPO_RUNTIME_VERSION"
124-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:19:16-72
125            android:value="@string/expo_runtime_version" />
125-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:19:73-117
126        <meta-data
126-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:20:5-105
127            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
127-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:20:16-80
128            android:value="ALWAYS" />
128-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:20:81-103
129        <meta-data
129-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:21:5-99
130            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
130-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:21:16-79
131            android:value="0" />
131-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:21:80-97
132
133        <activity
133-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:22:5-33:16
134            android:name="com.postureapp.android.MainActivity"
134-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:22:15-43
135            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
135-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:22:44-134
136            android:exported="true"
136-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:22:256-279
137            android:launchMode="singleTask"
137-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:22:135-166
138            android:screenOrientation="portrait"
138-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:22:280-316
139            android:theme="@style/Theme.App.SplashScreen"
139-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:22:210-255
140            android:windowSoftInputMode="adjustResize" >
140-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:22:167-209
141            <intent-filter>
141-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:23:7-26:23
142                <action android:name="android.intent.action.MAIN" />
142-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:24:9-60
142-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:24:17-58
143
144                <category android:name="android.intent.category.LAUNCHER" />
144-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:25:9-68
144-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:25:19-66
145            </intent-filter>
146            <intent-filter>
146-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:27:7-32:23
147                <action android:name="android.intent.action.VIEW" />
147-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:12:7-58
147-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:12:15-56
148
149                <category android:name="android.intent.category.DEFAULT" />
149-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:29:9-67
149-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:29:19-65
150                <category android:name="android.intent.category.BROWSABLE" />
150-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:13:7-67
150-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:13:17-65
151
152                <data android:scheme="postureapp" />
152-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:7-37
152-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:13-35
153            </intent-filter>
154        </activity>
155        <activity
155-->[:react-native-razorpay] D:\PostureApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:86
156            android:name="com.razorpay.CheckoutActivity"
156-->[:react-native-razorpay] D:\PostureApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-57
157            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
157-->[:react-native-razorpay] D:\PostureApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-83
158            android:exported="false"
158-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:45:13-37
159            android:theme="@style/CheckoutTheme" >
159-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:46:13-49
160            <intent-filter>
160-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:47:13-49:29
161                <action android:name="android.intent.action.MAIN" />
161-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:24:9-60
161-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:24:17-58
162            </intent-filter>
163        </activity>
164
165        <meta-data
165-->[:expo-modules-core] D:\PostureApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
166            android:name="org.unimodules.core.AppLoader#react-native-headless"
166-->[:expo-modules-core] D:\PostureApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
167            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
167-->[:expo-modules-core] D:\PostureApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
168        <meta-data
168-->[:expo-modules-core] D:\PostureApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
169            android:name="com.facebook.soloader.enabled"
169-->[:expo-modules-core] D:\PostureApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
170            android:value="true" />
170-->[:expo-modules-core] D:\PostureApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
171
172        <activity
172-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecf58a1d5a3b5cc4faabc13e873181f2\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
173            android:name="com.facebook.react.devsupport.DevSettingsActivity"
173-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecf58a1d5a3b5cc4faabc13e873181f2\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
174            android:exported="false" />
174-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecf58a1d5a3b5cc4faabc13e873181f2\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
175
176        <meta-data
176-->[host.exp.exponent:expo.modules.camera:16.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee6cfceb4b4a8f4d48ea5c0fc6dc649c\transformed\expo.modules.camera-16.1.11\AndroidManifest.xml:11:9-13:42
177            android:name="com.google.mlkit.vision.DEPENDENCIES"
177-->[host.exp.exponent:expo.modules.camera:16.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee6cfceb4b4a8f4d48ea5c0fc6dc649c\transformed\expo.modules.camera-16.1.11\AndroidManifest.xml:12:13-64
178            android:value="barcode_ui" />
178-->[host.exp.exponent:expo.modules.camera:16.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee6cfceb4b4a8f4d48ea5c0fc6dc649c\transformed\expo.modules.camera-16.1.11\AndroidManifest.xml:13:13-39
179
180        <provider
180-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
181            android:name="expo.modules.filesystem.FileSystemFileProvider"
181-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
182            android:authorities="com.postureapp.android.FileSystemFileProvider"
182-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
183            android:exported="false"
183-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
184            android:grantUriPermissions="true" >
184-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
185            <meta-data
185-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:27:13-29:70
186                android:name="android.support.FILE_PROVIDER_PATHS"
186-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:28:17-67
187                android:resource="@xml/file_system_provider_paths" />
187-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:29:17-67
188        </provider>
189
190        <service
190-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:11:9-17:19
191            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
191-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:12:13-91
192            android:exported="false" >
192-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:13:13-37
193            <intent-filter android:priority="-1" >
193-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:13-16:29
193-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:28-49
194                <action android:name="com.google.firebase.MESSAGING_EVENT" />
194-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:17-78
194-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:25-75
195            </intent-filter>
196        </service>
197
198        <receiver
198-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:19:9-31:20
199            android:name="expo.modules.notifications.service.NotificationsService"
199-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:20:13-83
200            android:enabled="true"
200-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:21:13-35
201            android:exported="false" >
201-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:22:13-37
202            <intent-filter android:priority="-1" >
202-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:23:13-30:29
202-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:23:28-49
203                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
203-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:24:17-88
203-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:24:25-85
204                <action android:name="android.intent.action.BOOT_COMPLETED" />
204-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:25:17-79
204-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:25:25-76
205                <action android:name="android.intent.action.REBOOT" />
205-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:26:17-71
205-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:26:25-68
206                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
206-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:27:17-82
206-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:27:25-79
207                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
207-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:28:17-82
207-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:28:25-79
208                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
208-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:29:17-84
208-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:29:25-81
209            </intent-filter>
210        </receiver>
211
212        <activity
212-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:33:9-40:75
213            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
213-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:34:13-92
214            android:excludeFromRecents="true"
214-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:35:13-46
215            android:exported="false"
215-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:36:13-37
216            android:launchMode="standard"
216-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:37:13-42
217            android:noHistory="true"
217-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:38:13-37
218            android:taskAffinity=""
218-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:39:13-36
219            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
219-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:40:13-72
220
221        <provider
221-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:52:9-60:20
222            android:name="androidx.startup.InitializationProvider"
222-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:53:13-67
223            android:authorities="com.postureapp.android.androidx-startup"
223-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:54:13-68
224            android:exported="false" >
224-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:55:13-37
225            <meta-data
225-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:57:13-59:52
226                android:name="com.razorpay.RazorpayInitializer"
226-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:58:17-64
227                android:value="androidx.startup" />
227-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:59:17-49
228            <meta-data
228-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
229                android:name="androidx.emoji2.text.EmojiCompatInitializer"
229-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
230                android:value="androidx.startup" />
230-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
231            <meta-data
231-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ea4441ccd6266f533a7e0524b0289c3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
232                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
232-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ea4441ccd6266f533a7e0524b0289c3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
233                android:value="androidx.startup" />
233-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ea4441ccd6266f533a7e0524b0289c3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
234            <meta-data
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
235                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
236                android:value="androidx.startup" />
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
237        </provider>
238
239        <activity
239-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:62:9-65:75
240            android:name="com.razorpay.MagicXActivity"
240-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:63:13-55
241            android:exported="false"
241-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:64:13-37
242            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
242-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:65:13-72
243
244        <meta-data
244-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:67:9-69:58
245            android:name="com.razorpay.plugin.googlepay_all"
245-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:68:13-61
246            android:value="com.razorpay.RzpGpayMerged" />
246-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:69:13-55
247
248        <receiver
248-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
249            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
249-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
250            android:exported="true"
250-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
251            android:permission="com.google.android.c2dm.permission.SEND" >
251-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
252            <intent-filter>
252-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
253                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
253-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
253-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
254            </intent-filter>
255
256            <meta-data
256-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
257                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
257-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
258                android:value="true" />
258-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
259        </receiver>
260        <!--
261             FirebaseMessagingService performs security checks at runtime,
262             but set to not exported to explicitly avoid allowing another app to call it.
263        -->
264        <service
264-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
265            android:name="com.google.firebase.messaging.FirebaseMessagingService"
265-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
266            android:directBootAware="true"
266-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
267            android:exported="false" >
267-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
268            <intent-filter android:priority="-500" >
268-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:13-16:29
268-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:28-49
269                <action android:name="com.google.firebase.MESSAGING_EVENT" />
269-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:17-78
269-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:25-75
270            </intent-filter>
271        </service>
272        <service
272-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
273            android:name="com.google.firebase.components.ComponentDiscoveryService"
273-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
274            android:directBootAware="true"
274-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
275            android:exported="false" >
275-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
276            <meta-data
276-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
277                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
277-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
278                android:value="com.google.firebase.components.ComponentRegistrar" />
278-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
279            <meta-data
279-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
280                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
280-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
281                android:value="com.google.firebase.components.ComponentRegistrar" />
281-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
282            <meta-data
282-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9ac7abbee3fc01e4af1dd52a78ad74f\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
283                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
283-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9ac7abbee3fc01e4af1dd52a78ad74f\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
284                android:value="com.google.firebase.components.ComponentRegistrar" />
284-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9ac7abbee3fc01e4af1dd52a78ad74f\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
285            <meta-data
285-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9ac7abbee3fc01e4af1dd52a78ad74f\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
286                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
286-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9ac7abbee3fc01e4af1dd52a78ad74f\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
287                android:value="com.google.firebase.components.ComponentRegistrar" />
287-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9ac7abbee3fc01e4af1dd52a78ad74f\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
288            <meta-data
288-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f41a0a0abe9082c2adadbc91827429e5\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
289                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
289-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f41a0a0abe9082c2adadbc91827429e5\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
290                android:value="com.google.firebase.components.ComponentRegistrar" />
290-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f41a0a0abe9082c2adadbc91827429e5\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
291            <meta-data
291-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
292                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
292-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
293                android:value="com.google.firebase.components.ComponentRegistrar" />
293-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
294            <meta-data
294-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78a3c009a8dccc885176388c2c4753ad\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
295                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
295-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78a3c009a8dccc885176388c2c4753ad\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
296                android:value="com.google.firebase.components.ComponentRegistrar" />
296-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78a3c009a8dccc885176388c2c4753ad\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
297        </service>
298
299        <uses-library
299-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf8b5107ff437249fa41919294d3c2e3\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:29:9-31:40
300            android:name="androidx.camera.extensions.impl"
300-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf8b5107ff437249fa41919294d3c2e3\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:30:13-59
301            android:required="false" />
301-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf8b5107ff437249fa41919294d3c2e3\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:31:13-37
302
303        <service
303-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9579ce2f304c1543905cbc2d494490a\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:24:9-33:19
304            android:name="androidx.camera.core.impl.MetadataHolderService"
304-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9579ce2f304c1543905cbc2d494490a\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:25:13-75
305            android:enabled="false"
305-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9579ce2f304c1543905cbc2d494490a\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:26:13-36
306            android:exported="false" >
306-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9579ce2f304c1543905cbc2d494490a\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:27:13-37
307            <meta-data
307-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9579ce2f304c1543905cbc2d494490a\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:30:13-32:89
308                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
308-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9579ce2f304c1543905cbc2d494490a\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:31:17-103
309                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
309-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9579ce2f304c1543905cbc2d494490a\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:32:17-86
310        </service>
311        <service
311-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
312            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
312-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
313            android:directBootAware="true"
313-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
314            android:exported="false" >
314-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
315            <meta-data
315-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
316                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
316-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
317                android:value="com.google.firebase.components.ComponentRegistrar" />
317-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
318            <meta-data
318-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\136f34202ce7744e681e139afd02dc79\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
319                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
319-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\136f34202ce7744e681e139afd02dc79\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
320                android:value="com.google.firebase.components.ComponentRegistrar" />
320-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\136f34202ce7744e681e139afd02dc79\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
321            <meta-data
321-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
322                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
322-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
323                android:value="com.google.firebase.components.ComponentRegistrar" />
323-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
324        </service>
325        <!--
326        This activity is an invisible delegate activity to start scanner activity
327        and receive result, so it's unnecessary to support screen orientation and
328        we can avoid any side effect from activity recreation in any case.
329        -->
330        <activity
330-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\caf96b069381c5a664eb42472df52a32\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
331            android:name="com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity"
331-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\caf96b069381c5a664eb42472df52a32\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
332            android:exported="false"
332-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\caf96b069381c5a664eb42472df52a32\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
333            android:screenOrientation="portrait" >
333-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\caf96b069381c5a664eb42472df52a32\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
334        </activity>
335
336        <provider
336-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
337            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
337-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
338            android:authorities="com.postureapp.android.mlkitinitprovider"
338-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
339            android:exported="false"
339-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
340            android:initOrder="99" />
340-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
341
342        <activity
342-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
343            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
343-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
344            android:excludeFromRecents="true"
344-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
345            android:exported="false"
345-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
346            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
346-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
347        <!--
348            Service handling Google Sign-In user revocation. For apps that do not integrate with
349            Google Sign-In, this service will never be started.
350        -->
351        <service
351-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
352            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
352-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
353            android:exported="true"
353-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
354            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
354-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
355            android:visibleToInstantApps="true" /> <!-- Needs to be explicitly declared on P+ -->
355-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
356        <uses-library
356-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29f3e55b4764c3def309e3d61ebb4352\transformed\play-services-maps-17.0.0\AndroidManifest.xml:33:9-35:40
357            android:name="org.apache.http.legacy"
357-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29f3e55b4764c3def309e3d61ebb4352\transformed\play-services-maps-17.0.0\AndroidManifest.xml:34:13-50
358            android:required="false" />
358-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29f3e55b4764c3def309e3d61ebb4352\transformed\play-services-maps-17.0.0\AndroidManifest.xml:35:13-37
359
360        <activity
360-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf4e5700f050532bbd59ead7b0c07184\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
361            android:name="com.google.android.gms.common.api.GoogleApiActivity"
361-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf4e5700f050532bbd59ead7b0c07184\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
362            android:exported="false"
362-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf4e5700f050532bbd59ead7b0c07184\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
363            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
363-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf4e5700f050532bbd59ead7b0c07184\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
364
365        <provider
365-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
366            android:name="com.google.firebase.provider.FirebaseInitProvider"
366-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
367            android:authorities="com.postureapp.android.firebaseinitprovider"
367-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
368            android:directBootAware="true"
368-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
369            android:exported="false"
369-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
370            android:initOrder="100" />
370-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
371
372        <meta-data
372-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c47125c9a335e989accf2286b6eb952\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
373            android:name="com.google.android.gms.version"
373-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c47125c9a335e989accf2286b6eb952\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
374            android:value="@integer/google_play_services_version" />
374-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c47125c9a335e989accf2286b6eb952\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
375
376        <receiver
376-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
377            android:name="androidx.profileinstaller.ProfileInstallReceiver"
377-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
378            android:directBootAware="false"
378-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
379            android:enabled="true"
379-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
380            android:exported="true"
380-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
381            android:permission="android.permission.DUMP" >
381-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
382            <intent-filter>
382-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
383                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
383-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
383-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
384            </intent-filter>
385            <intent-filter>
385-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
386                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
386-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
386-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
387            </intent-filter>
388            <intent-filter>
388-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
389                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
389-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
389-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
390            </intent-filter>
391            <intent-filter>
391-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
392                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
392-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
392-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
393            </intent-filter>
394        </receiver>
395
396        <service
396-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f44a37c7456c771ca1923556c09af4d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
397            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
397-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f44a37c7456c771ca1923556c09af4d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
398            android:exported="false" >
398-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f44a37c7456c771ca1923556c09af4d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
399            <meta-data
399-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f44a37c7456c771ca1923556c09af4d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
400                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
400-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f44a37c7456c771ca1923556c09af4d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
401                android:value="cct" />
401-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f44a37c7456c771ca1923556c09af4d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
402        </service>
403        <service
403-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\027d67677131eabd10f58abcc4dd8a4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
404            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
404-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\027d67677131eabd10f58abcc4dd8a4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
405            android:exported="false"
405-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\027d67677131eabd10f58abcc4dd8a4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
406            android:permission="android.permission.BIND_JOB_SERVICE" >
406-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\027d67677131eabd10f58abcc4dd8a4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
407        </service>
408
409        <receiver
409-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\027d67677131eabd10f58abcc4dd8a4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
410            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
410-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\027d67677131eabd10f58abcc4dd8a4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
411            android:exported="false" />
411-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\027d67677131eabd10f58abcc4dd8a4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
412    </application>
413
414</manifest>
