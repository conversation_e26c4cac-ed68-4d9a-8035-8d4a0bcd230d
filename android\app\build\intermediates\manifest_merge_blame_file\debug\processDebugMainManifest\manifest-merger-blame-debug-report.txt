1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.postureapp.android"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:7:3-75
11-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:7:20-73
12    <uses-permission android:name="android.permission.CAMERA" />
12-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:2:3-62
12-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:2:20-60
13    <uses-permission android:name="android.permission.INTERNET" />
13-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:3:3-64
13-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:3:20-62
14    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
14-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:4:3-77
14-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:4:20-75
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:5:3-77
15-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:5:20-75
16    <uses-permission android:name="android.permission.RECORD_AUDIO" />
16-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:6:3-68
16-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:6:20-66
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:8:3-63
17-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:8:20-61
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:9:3-78
18-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:9:20-76
19
20    <queries>
20-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:10:3-16:13
21        <intent>
21-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:11:5-15:14
22            <action android:name="android.intent.action.VIEW" />
22-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:12:7-58
22-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:12:15-56
23
24            <category android:name="android.intent.category.BROWSABLE" />
24-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:13:7-67
24-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:13:17-65
25
26            <data android:scheme="https" />
26-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:7-37
26-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:13-35
27        </intent>
28        <!-- Query open documents -->
29        <intent>
29-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
30            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
30-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
30-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
31        </intent>
32        <intent>
32-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:11:9-17:18
33            <action android:name="android.intent.action.VIEW" />
33-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:12:7-58
33-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:12:15-56
34
35            <data
35-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:7-37
36                android:mimeType="*/*"
37                android:scheme="*" />
37-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:13-35
38        </intent>
39        <intent>
39-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:18:9-27:18
40            <action android:name="android.intent.action.VIEW" />
40-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:12:7-58
40-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:12:15-56
41
42            <category android:name="android.intent.category.BROWSABLE" />
42-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:13:7-67
42-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:13:17-65
43
44            <data
44-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:7-37
45                android:host="pay"
46                android:mimeType="*/*"
47                android:scheme="upi" />
47-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:13-35
48        </intent>
49        <intent>
49-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:28:9-30:18
50            <action android:name="android.intent.action.MAIN" />
50-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:24:9-60
50-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:24:17-58
51        </intent>
52        <intent>
52-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:31:9-35:18
53            <action android:name="android.intent.action.SEND" />
53-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:32:13-65
53-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:32:21-62
54
55            <data android:mimeType="*/*" />
55-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:7-37
56        </intent>
57        <intent>
57-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:36:9-38:18
58            <action android:name="rzp.device_token.share" />
58-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:37:13-61
58-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:37:21-58
59        </intent>
60        <intent>
60-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf8b5107ff437249fa41919294d3c2e3\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:23:9-25:18
61            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
61-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf8b5107ff437249fa41919294d3c2e3\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:24:13-86
61-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf8b5107ff437249fa41919294d3c2e3\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:24:21-83
62        </intent>
63    </queries>
64
65    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
65-->[:react-native-community_netinfo] D:\PostureApp\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
65-->[:react-native-community_netinfo] D:\PostureApp\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
66    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
66-->[:react-native-community_netinfo] D:\PostureApp\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
66-->[:react-native-community_netinfo] D:\PostureApp\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
67
68    <uses-feature
68-->[host.exp.exponent:expo.modules.gl:15.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d4d19beb0084614942547ad86945cca\transformed\expo.modules.gl-15.1.7\AndroidManifest.xml:7:5-9:36
69        android:glEsVersion="0x00020000"
69-->[host.exp.exponent:expo.modules.gl:15.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d4d19beb0084614942547ad86945cca\transformed\expo.modules.gl-15.1.7\AndroidManifest.xml:8:9-41
70        android:required="true" />
70-->[host.exp.exponent:expo.modules.gl:15.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d4d19beb0084614942547ad86945cca\transformed\expo.modules.gl-15.1.7\AndroidManifest.xml:9:9-33
71
72    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
72-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:7:5-81
72-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:7:22-78
73    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
73-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:8:5-77
73-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:8:22-74
74    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
74-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
74-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:22-65
75    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
75-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
75-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
76    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
76-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88b9f6ad7ed87d7d30486cdd2fcbb5f7\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
76-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88b9f6ad7ed87d7d30486cdd2fcbb5f7\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
77    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
77-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88b9f6ad7ed87d7d30486cdd2fcbb5f7\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
77-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88b9f6ad7ed87d7d30486cdd2fcbb5f7\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
78
79    <permission
79-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
80        android:name="com.postureapp.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
80-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
81        android:protectionLevel="signature" />
81-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
82
83    <uses-permission android:name="com.postureapp.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
83-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
83-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
84    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
84-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e75f83e5e2bd4ad05880a4e7cced326b\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
84-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e75f83e5e2bd4ad05880a4e7cced326b\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
85    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
86    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
87    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
88    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
89    <!-- for Samsung -->
90    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
91    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
92    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
92-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
93    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
93-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
94    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
94-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
95    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
95-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
96    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
96-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
96-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
97    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
97-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
97-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
98    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
98-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
98-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
99    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
99-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
99-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
100    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
100-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
100-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
101    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
101-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
101-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
102    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
102-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
102-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
103    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
103-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
103-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
104    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
104-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
104-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
105    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
105-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
105-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a37c5ffb528e6a5fb2c4356e4fa9bb8\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
106
107    <application
107-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:3-34:17
108        android:name="com.postureapp.android.MainApplication"
108-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:16-47
109        android:allowBackup="true"
109-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:162-188
110        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
110-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
111        android:dataExtractionRules="@xml/secure_store_data_extraction_rules"
111-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:307-376
112        android:debuggable="true"
113        android:extractNativeLibs="false"
114        android:fullBackupContent="@xml/secure_store_backup_rules"
114-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:248-306
115        android:icon="@mipmap/ic_launcher"
115-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:81-115
116        android:label="@string/app_name"
116-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:48-80
117        android:roundIcon="@mipmap/ic_launcher_round"
117-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:116-161
118        android:supportsRtl="true"
118-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:221-247
119        android:theme="@style/AppTheme"
119-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:17:189-220
120        android:usesCleartextTraffic="true" >
120-->D:\PostureApp\android\app\src\debug\AndroidManifest.xml:6:18-53
121        <meta-data
121-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:18:5-83
122            android:name="expo.modules.updates.ENABLED"
122-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:18:16-59
123            android:value="false" />
123-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:18:60-81
124        <meta-data
124-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:19:5-119
125            android:name="expo.modules.updates.EXPO_RUNTIME_VERSION"
125-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:19:16-72
126            android:value="@string/expo_runtime_version" />
126-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:19:73-117
127        <meta-data
127-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:20:5-105
128            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
128-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:20:16-80
129            android:value="ALWAYS" />
129-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:20:81-103
130        <meta-data
130-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:21:5-99
131            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
131-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:21:16-79
132            android:value="0" />
132-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:21:80-97
133
134        <activity
134-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:22:5-33:16
135            android:name="com.postureapp.android.MainActivity"
135-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:22:15-43
136            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
136-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:22:44-134
137            android:exported="true"
137-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:22:256-279
138            android:launchMode="singleTask"
138-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:22:135-166
139            android:screenOrientation="portrait"
139-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:22:280-316
140            android:theme="@style/Theme.App.SplashScreen"
140-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:22:210-255
141            android:windowSoftInputMode="adjustResize" >
141-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:22:167-209
142            <intent-filter>
142-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:23:7-26:23
143                <action android:name="android.intent.action.MAIN" />
143-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:24:9-60
143-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:24:17-58
144
145                <category android:name="android.intent.category.LAUNCHER" />
145-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:25:9-68
145-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:25:19-66
146            </intent-filter>
147            <intent-filter>
147-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:27:7-32:23
148                <action android:name="android.intent.action.VIEW" />
148-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:12:7-58
148-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:12:15-56
149
150                <category android:name="android.intent.category.DEFAULT" />
150-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:29:9-67
150-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:29:19-65
151                <category android:name="android.intent.category.BROWSABLE" />
151-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:13:7-67
151-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:13:17-65
152
153                <data android:scheme="postureapp" />
153-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:7-37
153-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:14:13-35
154            </intent-filter>
155        </activity>
156        <activity
156-->[:react-native-razorpay] D:\PostureApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:86
157            android:name="com.razorpay.CheckoutActivity"
157-->[:react-native-razorpay] D:\PostureApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-57
158            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
158-->[:react-native-razorpay] D:\PostureApp\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-83
159            android:exported="false"
159-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:45:13-37
160            android:theme="@style/CheckoutTheme" >
160-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:46:13-49
161            <intent-filter>
161-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:47:13-49:29
162                <action android:name="android.intent.action.MAIN" />
162-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:24:9-60
162-->D:\PostureApp\android\app\src\main\AndroidManifest.xml:24:17-58
163            </intent-filter>
164        </activity>
165
166        <meta-data
166-->[:expo-modules-core] D:\PostureApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
167            android:name="org.unimodules.core.AppLoader#react-native-headless"
167-->[:expo-modules-core] D:\PostureApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
168            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
168-->[:expo-modules-core] D:\PostureApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
169        <meta-data
169-->[:expo-modules-core] D:\PostureApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
170            android:name="com.facebook.soloader.enabled"
170-->[:expo-modules-core] D:\PostureApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
171            android:value="true" />
171-->[:expo-modules-core] D:\PostureApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
172
173        <activity
173-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecf58a1d5a3b5cc4faabc13e873181f2\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
174            android:name="com.facebook.react.devsupport.DevSettingsActivity"
174-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecf58a1d5a3b5cc4faabc13e873181f2\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
175            android:exported="false" />
175-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecf58a1d5a3b5cc4faabc13e873181f2\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
176
177        <meta-data
177-->[host.exp.exponent:expo.modules.camera:16.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee6cfceb4b4a8f4d48ea5c0fc6dc649c\transformed\expo.modules.camera-16.1.11\AndroidManifest.xml:11:9-13:42
178            android:name="com.google.mlkit.vision.DEPENDENCIES"
178-->[host.exp.exponent:expo.modules.camera:16.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee6cfceb4b4a8f4d48ea5c0fc6dc649c\transformed\expo.modules.camera-16.1.11\AndroidManifest.xml:12:13-64
179            android:value="barcode_ui" />
179-->[host.exp.exponent:expo.modules.camera:16.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee6cfceb4b4a8f4d48ea5c0fc6dc649c\transformed\expo.modules.camera-16.1.11\AndroidManifest.xml:13:13-39
180
181        <provider
181-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
182            android:name="expo.modules.filesystem.FileSystemFileProvider"
182-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
183            android:authorities="com.postureapp.android.FileSystemFileProvider"
183-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
184            android:exported="false"
184-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
185            android:grantUriPermissions="true" >
185-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
186            <meta-data
186-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:27:13-29:70
187                android:name="android.support.FILE_PROVIDER_PATHS"
187-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:28:17-67
188                android:resource="@xml/file_system_provider_paths" />
188-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:29:17-67
189        </provider>
190
191        <service
191-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:11:9-17:19
192            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
192-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:12:13-91
193            android:exported="false" >
193-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:13:13-37
194            <intent-filter android:priority="-1" >
194-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:13-16:29
194-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:28-49
195                <action android:name="com.google.firebase.MESSAGING_EVENT" />
195-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:17-78
195-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:25-75
196            </intent-filter>
197        </service>
198
199        <receiver
199-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:19:9-31:20
200            android:name="expo.modules.notifications.service.NotificationsService"
200-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:20:13-83
201            android:enabled="true"
201-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:21:13-35
202            android:exported="false" >
202-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:22:13-37
203            <intent-filter android:priority="-1" >
203-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:23:13-30:29
203-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:23:28-49
204                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
204-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:24:17-88
204-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:24:25-85
205                <action android:name="android.intent.action.BOOT_COMPLETED" />
205-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:25:17-79
205-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:25:25-76
206                <action android:name="android.intent.action.REBOOT" />
206-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:26:17-71
206-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:26:25-68
207                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
207-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:27:17-82
207-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:27:25-79
208                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
208-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:28:17-82
208-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:28:25-79
209                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
209-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:29:17-84
209-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:29:25-81
210            </intent-filter>
211        </receiver>
212
213        <activity
213-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:33:9-40:75
214            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
214-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:34:13-92
215            android:excludeFromRecents="true"
215-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:35:13-46
216            android:exported="false"
216-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:36:13-37
217            android:launchMode="standard"
217-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:37:13-42
218            android:noHistory="true"
218-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:38:13-37
219            android:taskAffinity=""
219-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:39:13-36
220            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
220-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:40:13-72
221
222        <provider
222-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:52:9-60:20
223            android:name="androidx.startup.InitializationProvider"
223-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:53:13-67
224            android:authorities="com.postureapp.android.androidx-startup"
224-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:54:13-68
225            android:exported="false" >
225-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:55:13-37
226            <meta-data
226-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:57:13-59:52
227                android:name="com.razorpay.RazorpayInitializer"
227-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:58:17-64
228                android:value="androidx.startup" />
228-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:59:17-49
229            <meta-data
229-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
230                android:name="androidx.emoji2.text.EmojiCompatInitializer"
230-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
231                android:value="androidx.startup" />
231-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
232            <meta-data
232-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ea4441ccd6266f533a7e0524b0289c3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
233                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
233-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ea4441ccd6266f533a7e0524b0289c3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
234                android:value="androidx.startup" />
234-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ea4441ccd6266f533a7e0524b0289c3\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
235            <meta-data
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
236                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
237                android:value="androidx.startup" />
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
238        </provider>
239
240        <activity
240-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:62:9-65:75
241            android:name="com.razorpay.MagicXActivity"
241-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:63:13-55
242            android:exported="false"
242-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:64:13-37
243            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
243-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:65:13-72
244
245        <meta-data
245-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:67:9-69:58
246            android:name="com.razorpay.plugin.googlepay_all"
246-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:68:13-61
247            android:value="com.razorpay.RzpGpayMerged" />
247-->[com.razorpay:standard-core:1.6.54] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\AndroidManifest.xml:69:13-55
248
249        <receiver
249-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
250            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
250-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
251            android:exported="true"
251-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
252            android:permission="com.google.android.c2dm.permission.SEND" >
252-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
253            <intent-filter>
253-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
254                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
254-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
254-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
255            </intent-filter>
256
257            <meta-data
257-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
258                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
258-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
259                android:value="true" />
259-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
260        </receiver>
261        <!--
262             FirebaseMessagingService performs security checks at runtime,
263             but set to not exported to explicitly avoid allowing another app to call it.
264        -->
265        <service
265-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
266            android:name="com.google.firebase.messaging.FirebaseMessagingService"
266-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
267            android:directBootAware="true"
267-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
268            android:exported="false" >
268-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
269            <intent-filter android:priority="-500" >
269-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:13-16:29
269-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:28-49
270                <action android:name="com.google.firebase.MESSAGING_EVENT" />
270-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:17-78
270-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:25-75
271            </intent-filter>
272        </service>
273        <service
273-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
274            android:name="com.google.firebase.components.ComponentDiscoveryService"
274-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
275            android:directBootAware="true"
275-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
276            android:exported="false" >
276-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
277            <meta-data
277-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
278                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
278-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
279                android:value="com.google.firebase.components.ComponentRegistrar" />
279-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
280            <meta-data
280-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
281                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
281-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
282                android:value="com.google.firebase.components.ComponentRegistrar" />
282-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
283            <meta-data
283-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9ac7abbee3fc01e4af1dd52a78ad74f\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
284                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
284-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9ac7abbee3fc01e4af1dd52a78ad74f\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
285                android:value="com.google.firebase.components.ComponentRegistrar" />
285-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9ac7abbee3fc01e4af1dd52a78ad74f\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
286            <meta-data
286-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9ac7abbee3fc01e4af1dd52a78ad74f\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
287                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
287-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9ac7abbee3fc01e4af1dd52a78ad74f\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
288                android:value="com.google.firebase.components.ComponentRegistrar" />
288-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d9ac7abbee3fc01e4af1dd52a78ad74f\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
289            <meta-data
289-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f41a0a0abe9082c2adadbc91827429e5\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
290                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
290-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f41a0a0abe9082c2adadbc91827429e5\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
291                android:value="com.google.firebase.components.ComponentRegistrar" />
291-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f41a0a0abe9082c2adadbc91827429e5\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
292            <meta-data
292-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
293                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
293-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
294                android:value="com.google.firebase.components.ComponentRegistrar" />
294-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
295            <meta-data
295-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78a3c009a8dccc885176388c2c4753ad\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
296                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
296-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78a3c009a8dccc885176388c2c4753ad\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
297                android:value="com.google.firebase.components.ComponentRegistrar" />
297-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78a3c009a8dccc885176388c2c4753ad\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
298        </service>
299
300        <uses-library
300-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf8b5107ff437249fa41919294d3c2e3\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:29:9-31:40
301            android:name="androidx.camera.extensions.impl"
301-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf8b5107ff437249fa41919294d3c2e3\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:30:13-59
302            android:required="false" />
302-->[androidx.camera:camera-extensions:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf8b5107ff437249fa41919294d3c2e3\transformed\camera-extensions-1.5.0-alpha03\AndroidManifest.xml:31:13-37
303
304        <service
304-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9579ce2f304c1543905cbc2d494490a\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:24:9-33:19
305            android:name="androidx.camera.core.impl.MetadataHolderService"
305-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9579ce2f304c1543905cbc2d494490a\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:25:13-75
306            android:enabled="false"
306-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9579ce2f304c1543905cbc2d494490a\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:26:13-36
307            android:exported="false" >
307-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9579ce2f304c1543905cbc2d494490a\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:27:13-37
308            <meta-data
308-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9579ce2f304c1543905cbc2d494490a\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:30:13-32:89
309                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
309-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9579ce2f304c1543905cbc2d494490a\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:31:17-103
310                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
310-->[androidx.camera:camera-camera2:1.5.0-alpha03] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9579ce2f304c1543905cbc2d494490a\transformed\camera-camera2-1.5.0-alpha03\AndroidManifest.xml:32:17-86
311        </service>
312        <service
312-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
313            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
313-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
314            android:directBootAware="true"
314-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
315            android:exported="false" >
315-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
316            <meta-data
316-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
317                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
317-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
318                android:value="com.google.firebase.components.ComponentRegistrar" />
318-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8214a2666b7dd409fa57ef11f1eb8cb2\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
319            <meta-data
319-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\136f34202ce7744e681e139afd02dc79\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
320                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
320-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\136f34202ce7744e681e139afd02dc79\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
321                android:value="com.google.firebase.components.ComponentRegistrar" />
321-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\136f34202ce7744e681e139afd02dc79\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
322            <meta-data
322-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
323                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
323-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
324                android:value="com.google.firebase.components.ComponentRegistrar" />
324-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
325        </service>
326        <!--
327        This activity is an invisible delegate activity to start scanner activity
328        and receive result, so it's unnecessary to support screen orientation and
329        we can avoid any side effect from activity recreation in any case.
330        -->
331        <activity
331-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\caf96b069381c5a664eb42472df52a32\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
332            android:name="com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity"
332-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\caf96b069381c5a664eb42472df52a32\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
333            android:exported="false"
333-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\caf96b069381c5a664eb42472df52a32\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
334            android:screenOrientation="portrait" >
334-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\caf96b069381c5a664eb42472df52a32\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
335        </activity>
336
337        <provider
337-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
338            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
338-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
339            android:authorities="com.postureapp.android.mlkitinitprovider"
339-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
340            android:exported="false"
340-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
341            android:initOrder="99" />
341-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d45f7819af5b9a35fd4bcfd4f8f6843\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
342
343        <activity
343-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
344            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
344-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
345            android:excludeFromRecents="true"
345-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
346            android:exported="false"
346-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
347            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
347-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
348        <!--
349            Service handling Google Sign-In user revocation. For apps that do not integrate with
350            Google Sign-In, this service will never be started.
351        -->
352        <service
352-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
353            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
353-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
354            android:exported="true"
354-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
355            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
355-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
356            android:visibleToInstantApps="true" /> <!-- Needs to be explicitly declared on P+ -->
356-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
357        <uses-library
357-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29f3e55b4764c3def309e3d61ebb4352\transformed\play-services-maps-17.0.0\AndroidManifest.xml:33:9-35:40
358            android:name="org.apache.http.legacy"
358-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29f3e55b4764c3def309e3d61ebb4352\transformed\play-services-maps-17.0.0\AndroidManifest.xml:34:13-50
359            android:required="false" />
359-->[com.google.android.gms:play-services-maps:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29f3e55b4764c3def309e3d61ebb4352\transformed\play-services-maps-17.0.0\AndroidManifest.xml:35:13-37
360
361        <activity
361-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf4e5700f050532bbd59ead7b0c07184\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
362            android:name="com.google.android.gms.common.api.GoogleApiActivity"
362-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf4e5700f050532bbd59ead7b0c07184\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
363            android:exported="false"
363-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf4e5700f050532bbd59ead7b0c07184\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
364            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
364-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf4e5700f050532bbd59ead7b0c07184\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
365
366        <provider
366-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
367            android:name="com.google.firebase.provider.FirebaseInitProvider"
367-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
368            android:authorities="com.postureapp.android.firebaseinitprovider"
368-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
369            android:directBootAware="true"
369-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
370            android:exported="false"
370-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
371            android:initOrder="100" />
371-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
372
373        <meta-data
373-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c47125c9a335e989accf2286b6eb952\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
374            android:name="com.google.android.gms.version"
374-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c47125c9a335e989accf2286b6eb952\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
375            android:value="@integer/google_play_services_version" />
375-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c47125c9a335e989accf2286b6eb952\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
376
377        <receiver
377-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
378            android:name="androidx.profileinstaller.ProfileInstallReceiver"
378-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
379            android:directBootAware="false"
379-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
380            android:enabled="true"
380-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
381            android:exported="true"
381-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
382            android:permission="android.permission.DUMP" >
382-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
383            <intent-filter>
383-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
384                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
384-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
384-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
385            </intent-filter>
386            <intent-filter>
386-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
387                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
387-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
387-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
388            </intent-filter>
389            <intent-filter>
389-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
390                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
390-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
390-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
391            </intent-filter>
392            <intent-filter>
392-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
393                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
393-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
393-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
394            </intent-filter>
395        </receiver>
396
397        <service
397-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f44a37c7456c771ca1923556c09af4d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
398            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
398-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f44a37c7456c771ca1923556c09af4d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
399            android:exported="false" >
399-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f44a37c7456c771ca1923556c09af4d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
400            <meta-data
400-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f44a37c7456c771ca1923556c09af4d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
401                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
401-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f44a37c7456c771ca1923556c09af4d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
402                android:value="cct" />
402-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f44a37c7456c771ca1923556c09af4d\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
403        </service>
404        <service
404-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\027d67677131eabd10f58abcc4dd8a4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
405            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
405-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\027d67677131eabd10f58abcc4dd8a4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
406            android:exported="false"
406-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\027d67677131eabd10f58abcc4dd8a4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
407            android:permission="android.permission.BIND_JOB_SERVICE" >
407-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\027d67677131eabd10f58abcc4dd8a4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
408        </service>
409
410        <receiver
410-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\027d67677131eabd10f58abcc4dd8a4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
411            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
411-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\027d67677131eabd10f58abcc4dd8a4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
412            android:exported="false" />
412-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\027d67677131eabd10f58abcc4dd8a4e\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
413    </application>
414
415</manifest>
