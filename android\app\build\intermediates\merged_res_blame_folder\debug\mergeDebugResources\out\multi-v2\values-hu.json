{"logs": [{"outputFile": "com.postureapp.android.app-mergeDebugResources-67:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9a9d7ace362c65c6063a55648731b1\\transformed\\appcompat-1.7.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "950,1058,1150,1265,1349,1464,1587,1664,1739,1830,1923,2018,2112,2212,2305,2400,2495,2586,2677,2760,2870,2980,3080,3191,3300,3419,3601,19075", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "1053,1145,1260,1344,1459,1582,1659,1734,1825,1918,2013,2107,2207,2300,2395,2490,2581,2672,2755,2865,2975,3075,3186,3295,3414,3596,3699,19154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bf4e5700f050532bbd59ead7b0c07184\\transformed\\play-services-base-18.5.0\\res\\values-hu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,480,614,719,883,1017,1135,1241,1407,1511,1692,1825,1993,2161,2228,2292", "endColumns": "106,179,133,104,163,133,117,105,165,103,180,132,167,167,66,63,83", "endOffsets": "299,479,613,718,882,1016,1134,1240,1406,1510,1691,1824,1992,2160,2227,2291,2375"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5292,5403,5587,5725,5834,6002,6140,6262,6549,6719,6827,7012,7149,7321,7493,7564,7632", "endColumns": "110,183,137,108,167,137,121,109,169,107,184,136,171,171,70,67,87", "endOffsets": "5398,5582,5720,5829,5997,6135,6257,6367,6714,6822,7007,7144,7316,7488,7559,7627,7715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7becb12098085a58be85c10a694bf84d\\transformed\\material-1.12.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,587,667,766,886,969,1032,1096,1195,1270,1329,1439,1501,1570,1628,1700,1761,1816,1919,1976,2036,2091,2172,2292,2375,2453,2549,2635,2723,2858,2941,3021,3161,3255,3337,3390,3441,3507,3583,3665,3736,3820,3897,3972,4051,4128,4233,4329,4406,4498,4595,4669,4754,4851,4903,4986,5053,5141,5228,5290,5354,5417,5483,5581,5687,5781,5888,5945,6000,6085,6170,6247", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "258,339,415,492,582,662,761,881,964,1027,1091,1190,1265,1324,1434,1496,1565,1623,1695,1756,1811,1914,1971,2031,2086,2167,2287,2370,2448,2544,2630,2718,2853,2936,3016,3156,3250,3332,3385,3436,3502,3578,3660,3731,3815,3892,3967,4046,4123,4228,4324,4401,4493,4590,4664,4749,4846,4898,4981,5048,5136,5223,5285,5349,5412,5478,5576,5682,5776,5883,5940,5995,6080,6165,6242,6315"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,87,88,140,152,155,157,158,159,160,161,162,163,164,165,166,167,168,169,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,224,225,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,3782,3863,3939,4016,4106,4908,5007,5127,7923,7986,11895,13430,13651,13783,13893,13955,14024,14082,14154,14215,14270,14373,14430,14490,14545,14626,14961,15044,15122,15218,15304,15392,15527,15610,15690,15830,15924,16006,16059,16110,16176,16252,16334,16405,16489,16566,16641,16720,16797,16902,16998,17075,17167,17264,17338,17423,17520,17572,17655,17722,17810,17897,17959,18023,18086,18152,18250,18356,18450,18557,18614,18669,19159,19244,19321", "endLines": "22,51,52,53,54,55,63,64,65,87,88,140,152,155,157,158,159,160,161,162,163,164,165,166,167,168,169,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,224,225,226", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "945,3858,3934,4011,4101,4181,5002,5122,5205,7981,8045,11989,13500,13705,13888,13950,14019,14077,14149,14210,14265,14368,14425,14485,14540,14621,14741,15039,15117,15213,15299,15387,15522,15605,15685,15825,15919,16001,16054,16105,16171,16247,16329,16400,16484,16561,16636,16715,16792,16897,16993,17070,17162,17259,17333,17418,17515,17567,17650,17717,17805,17892,17954,18018,18081,18147,18245,18351,18445,18552,18609,18664,18749,19239,19316,19389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8c47125c9a335e989accf2286b6eb952\\transformed\\play-services-basement-18.4.0\\res\\values-hu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "172", "endOffsets": "367"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6372", "endColumns": "176", "endOffsets": "6544"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7bb9f3beb2453d006dfcb077349e135e\\transformed\\exoplayer-core-2.18.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,192,266,338,416,489,583,673", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "125,187,261,333,411,484,578,668,749"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10053,10128,10190,10264,10336,10414,10487,10581,10671", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "10123,10185,10259,10331,10409,10482,10576,10666,10747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\df4af9fc07c95635f45d375fd55e05f1\\transformed\\play-services-wallet-18.1.3\\res\\values-hu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "76", "endOffsets": "278"}, "to": {"startLines": "240", "startColumns": "4", "startOffsets": "20428", "endColumns": "80", "endOffsets": "20504"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\88b9f6ad7ed87d7d30486cdd2fcbb5f7\\transformed\\biometric-1.1.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,258,385,539,683,810,939,1098,1201,1342,1493", "endColumns": "110,91,126,153,143,126,128,158,102,140,150,129", "endOffsets": "161,253,380,534,678,805,934,1093,1196,1337,1488,1618"}, "to": {"startLines": "85,86,141,142,143,144,145,146,147,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7720,7831,11994,12121,12275,12419,12546,12675,12834,12937,13078,13229", "endColumns": "110,91,126,153,143,126,128,158,102,140,150,129", "endOffsets": "7826,7918,12116,12270,12414,12541,12670,12829,12932,13073,13224,13354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6c6e309b72c7c7f21fbfce9b95a8faf6\\transformed\\core-1.13.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "56,57,58,59,60,61,62,235", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4186,4283,4385,4487,4588,4691,4798,20024", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "4278,4380,4482,4583,4686,4793,4903,20120"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f3b883529050a580787eb1fe00d61c7b\\transformed\\exoplayer-ui-2.18.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,490,692,780,867,945,1031,1134,1208,1276,1373,1474,1547,1615,1680,1748,1861,1972,2082,2156,2238,2312,2385,2475,2564,2632,2695,2748,2806,2854,2915,2979,3046,3110,3178,3243,3302,3367,3433,3499,3552,3617,3699,3781", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,86,77,85,102,73,67,96,100,72,67,64,67,112,110,109,73,81,73,72,89,88,67,62,52,57,47,60,63,66,63,67,64,58,64,65,65,52,64,81,81,56", "endOffsets": "280,485,687,775,862,940,1026,1129,1203,1271,1368,1469,1542,1610,1675,1743,1856,1967,2077,2151,2233,2307,2380,2470,2559,2627,2690,2743,2801,2849,2910,2974,3041,3105,3173,3238,3297,3362,3428,3494,3547,3612,3694,3776,3833"}, "to": {"startLines": "2,11,15,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,585,8050,8138,8225,8303,8389,8492,8566,8634,8731,8832,8905,8973,9038,9106,9219,9330,9440,9514,9596,9670,9743,9833,9922,9990,10752,10805,10863,10911,10972,11036,11103,11167,11235,11300,11359,11424,11490,11556,11609,11674,11756,11838", "endLines": "10,14,18,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "endColumns": "17,12,12,87,86,77,85,102,73,67,96,100,72,67,64,67,112,110,109,73,81,73,72,89,88,67,62,52,57,47,60,63,66,63,67,64,58,64,65,65,52,64,81,81,56", "endOffsets": "375,580,782,8133,8220,8298,8384,8487,8561,8629,8726,8827,8900,8968,9033,9101,9214,9325,9435,9509,9591,9665,9738,9828,9917,9985,10048,10800,10858,10906,10967,11031,11098,11162,11230,11295,11354,11419,11485,11551,11604,11669,11751,11833,11890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ecf58a1d5a3b5cc4faabc13e873181f2\\transformed\\react-android-0.79.5-debug\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,215,286,353,432,505,572,645,720,803,892,963,1041,1120,1198,1283,1364,1440,1510,1579,1671,1746,1828,1899", "endColumns": "77,81,70,66,78,72,66,72,74,82,88,70,77,78,77,84,80,75,69,68,91,74,81,70,74", "endOffsets": "128,210,281,348,427,500,567,640,715,798,887,958,1036,1115,1193,1278,1359,1435,1505,1574,1666,1741,1823,1894,1969"}, "to": {"startLines": "50,66,151,153,154,156,170,171,172,219,220,221,222,227,228,229,230,231,232,233,234,236,237,238,239", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3704,5210,13359,13505,13572,13710,14746,14813,14886,18754,18837,18926,18997,19394,19473,19551,19636,19717,19793,19863,19932,20125,20200,20282,20353", "endColumns": "77,81,70,66,78,72,66,72,74,82,88,70,77,78,77,84,80,75,69,68,91,74,81,70,74", "endOffsets": "3777,5287,13425,13567,13646,13778,14808,14881,14956,18832,18921,18992,19070,19468,19546,19631,19712,19788,19858,19927,20019,20195,20277,20348,20423"}}]}]}