{"logs": [{"outputFile": "com.postureapp.android.app-mergeDebugResources-67:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bf4e5700f050532bbd59ead7b0c07184\\transformed\\play-services-base-18.5.0\\res\\values-kk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,441,563,665,801,923,1042,1147,1308,1410,1563,1688,1837,1990,2049,2104", "endColumns": "98,148,121,101,135,121,118,104,160,101,152,124,148,152,58,54,73", "endOffsets": "291,440,562,664,800,922,1041,1146,1307,1409,1562,1687,1836,1989,2048,2103,2177"}, "to": {"startLines": "66,67,68,69,70,71,72,73,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5137,5240,5393,5519,5625,5765,5891,6014,6287,6452,6558,6715,6844,6997,7154,7217,7276", "endColumns": "102,152,125,105,139,125,122,108,164,105,156,128,152,156,62,58,77", "endOffsets": "5235,5388,5514,5620,5760,5886,6009,6118,6447,6553,6710,6839,6992,7149,7212,7271,7349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8c47125c9a335e989accf2286b6eb952\\transformed\\play-services-basement-18.4.0\\res\\values-kk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "6123", "endColumns": "163", "endOffsets": "6282"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6c6e309b72c7c7f21fbfce9b95a8faf6\\transformed\\core-1.13.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "55,56,57,58,59,60,61,221", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4051,4146,4248,4350,4453,4557,4654,18587", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "4141,4243,4345,4448,4552,4649,4760,18683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f3b883529050a580787eb1fe00d61c7b\\transformed\\exoplayer-ui-2.18.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,483,677,757,837,924,1021,1118,1203,1268,1364,1461,1528,1593,1659,1729,1861,1992,2122,2198,2274,2348,2434,2523,2612,2678,2744,2797,2857,2905,2966,3026,3093,3158,3223,3286,3343,3415,3480,3546,3598,3659,3741,3823", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,79,79,86,96,96,84,64,95,96,66,64,65,69,131,130,129,75,75,73,85,88,88,65,65,52,59,47,60,59,66,64,64,62,56,71,64,65,51,60,81,81,54", "endOffsets": "281,478,672,752,832,919,1016,1113,1198,1263,1359,1456,1523,1588,1654,1724,1856,1987,2117,2193,2269,2343,2429,2518,2607,2673,2739,2792,2852,2900,2961,3021,3088,3153,3218,3281,3338,3410,3475,3541,3593,3654,3736,3818,3873"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,578,7683,7763,7843,7930,8027,8124,8209,8274,8370,8467,8534,8599,8665,8735,8867,8998,9128,9204,9280,9354,9440,9529,9618,9684,10454,10507,10567,10615,10676,10736,10803,10868,10933,10996,11053,11125,11190,11256,11308,11369,11451,11533", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,79,79,86,96,96,84,64,95,96,66,64,65,69,131,130,129,75,75,73,85,88,88,65,65,52,59,47,60,59,66,64,64,62,56,71,64,65,51,60,81,81,54", "endOffsets": "376,573,767,7758,7838,7925,8022,8119,8204,8269,8365,8462,8529,8594,8660,8730,8862,8993,9123,9199,9275,9349,9435,9524,9613,9679,9745,10502,10562,10610,10671,10731,10798,10863,10928,10991,11048,11120,11185,11251,11303,11364,11446,11528,11583"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9a9d7ace362c65c6063a55648731b1\\transformed\\appcompat-1.7.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "945,1053,1158,1268,1353,1459,1578,1658,1735,1826,1919,2014,2108,2208,2301,2396,2493,2584,2675,2756,2861,2964,3062,3169,3275,3375,3541,18118", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "1048,1153,1263,1348,1454,1573,1653,1730,1821,1914,2009,2103,2203,2296,2391,2488,2579,2670,2751,2856,2959,3057,3164,3270,3370,3536,3631,18195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\df4af9fc07c95635f45d375fd55e05f1\\transformed\\play-services-wallet-18.1.3\\res\\values-kk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "78", "endOffsets": "280"}, "to": {"startLines": "222", "startColumns": "4", "startOffsets": "18688", "endColumns": "82", "endOffsets": "18766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ecf58a1d5a3b5cc4faabc13e873181f2\\transformed\\react-android-0.79.5-debug\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,141,211,294,364,432,507", "endColumns": "85,69,82,69,67,74,72", "endOffsets": "136,206,289,359,427,502,575"}, "to": {"startLines": "65,151,152,154,168,219,220", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5051,13047,13117,13263,14305,18439,18514", "endColumns": "85,69,82,69,67,74,72", "endOffsets": "5132,13112,13195,13328,14368,18509,18582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\88b9f6ad7ed87d7d30486cdd2fcbb5f7\\transformed\\biometric-1.1.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,260,384,513,646,782,901,1040,1135,1284,1426", "endColumns": "114,89,123,128,132,135,118,138,94,148,141,127", "endOffsets": "165,255,379,508,641,777,896,1035,1130,1279,1421,1549"}, "to": {"startLines": "84,85,140,141,142,143,144,145,146,147,148,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7354,7469,11683,11807,11936,12069,12205,12324,12463,12558,12707,12849", "endColumns": "114,89,123,128,132,135,118,138,94,148,141,127", "endOffsets": "7464,7554,11802,11931,12064,12200,12319,12458,12553,12702,12844,12972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7becb12098085a58be85c10a694bf84d\\transformed\\material-1.12.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,427,506,600,688,780,892,974,1034,1098,1193,1263,1326,1433,1498,1565,1626,1693,1755,1809,1923,1982,2043,2097,2172,2298,2386,2472,2573,2663,2753,2895,2967,3040,3177,3266,3347,3404,3460,3526,3597,3674,3745,3825,3897,3973,4054,4124,4224,4311,4383,4474,4567,4641,4716,4808,4860,4942,5008,5092,5178,5240,5304,5367,5436,5540,5644,5738,5838,5899,5959,6043,6127,6203", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,78,93,87,91,111,81,59,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,85,100,89,89,141,71,72,136,88,80,56,55,65,70,76,70,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83,83,75,78", "endOffsets": "268,346,422,501,595,683,775,887,969,1029,1093,1188,1258,1321,1428,1493,1560,1621,1688,1750,1804,1918,1977,2038,2092,2167,2293,2381,2467,2568,2658,2748,2890,2962,3035,3172,3261,3342,3399,3455,3521,3592,3669,3740,3820,3892,3968,4049,4119,4219,4306,4378,4469,4562,4636,4711,4803,4855,4937,5003,5087,5173,5235,5299,5362,5431,5535,5639,5733,5833,5894,5954,6038,6122,6198,6277"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,86,87,139,150,153,155,156,157,158,159,160,161,162,163,164,165,166,167,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,216,217,218", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "772,3636,3714,3790,3869,3963,4765,4857,4969,7559,7619,11588,12977,13200,13333,13440,13505,13572,13633,13700,13762,13816,13930,13989,14050,14104,14179,14373,14461,14547,14648,14738,14828,14970,15042,15115,15252,15341,15422,15479,15535,15601,15672,15749,15820,15900,15972,16048,16129,16199,16299,16386,16458,16549,16642,16716,16791,16883,16935,17017,17083,17167,17253,17315,17379,17442,17511,17615,17719,17813,17913,17974,18034,18200,18284,18360", "endLines": "22,50,51,52,53,54,62,63,64,86,87,139,150,153,155,156,157,158,159,160,161,162,163,164,165,166,167,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,216,217,218", "endColumns": "12,77,75,78,93,87,91,111,81,59,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,85,100,89,89,141,71,72,136,88,80,56,55,65,70,76,70,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83,83,75,78", "endOffsets": "940,3709,3785,3864,3958,4046,4852,4964,5046,7614,7678,11678,13042,13258,13435,13500,13567,13628,13695,13757,13811,13925,13984,14045,14099,14174,14300,14456,14542,14643,14733,14823,14965,15037,15110,15247,15336,15417,15474,15530,15596,15667,15744,15815,15895,15967,16043,16124,16194,16294,16381,16453,16544,16637,16711,16786,16878,16930,17012,17078,17162,17248,17310,17374,17437,17506,17610,17714,17808,17908,17969,18029,18113,18279,18355,18434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7bb9f3beb2453d006dfcb077349e135e\\transformed\\exoplayer-core-2.18.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,184,252,316,401,488,585,681", "endColumns": "64,63,67,63,84,86,96,95,77", "endOffsets": "115,179,247,311,396,483,580,676,754"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9750,9815,9879,9947,10011,10096,10183,10280,10376", "endColumns": "64,63,67,63,84,86,96,95,77", "endOffsets": "9810,9874,9942,10006,10091,10178,10275,10371,10449"}}]}]}