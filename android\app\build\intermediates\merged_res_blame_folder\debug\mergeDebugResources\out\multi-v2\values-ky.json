{"logs": [{"outputFile": "com.postureapp.android.app-mergeDebugResources-67:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\88b9f6ad7ed87d7d30486cdd2fcbb5f7\\transformed\\biometric-1.1.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,254,374,521,650,800,925,1078,1177,1319,1478", "endColumns": "108,89,119,146,128,149,124,152,98,141,158,128", "endOffsets": "159,249,369,516,645,795,920,1073,1172,1314,1473,1602"}, "to": {"startLines": "84,85,140,141,142,143,144,145,146,147,148,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7475,7584,11799,11919,12066,12195,12345,12470,12623,12722,12864,13023", "endColumns": "108,89,119,146,128,149,124,152,98,141,158,128", "endOffsets": "7579,7669,11914,12061,12190,12340,12465,12618,12717,12859,13018,13147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9a9d7ace362c65c6063a55648731b1\\transformed\\appcompat-1.7.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "936,1047,1156,1268,1353,1458,1575,1654,1732,1823,1916,2011,2105,2205,2298,2393,2488,2579,2670,2751,2857,2962,3060,3167,3270,3385,3546,18306", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "1042,1151,1263,1348,1453,1570,1649,1727,1818,1911,2006,2100,2200,2293,2388,2483,2574,2665,2746,2852,2957,3055,3162,3265,3380,3541,3643,18383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ecf58a1d5a3b5cc4faabc13e873181f2\\transformed\\react-android-0.79.5-debug\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,143,212,295,365,432,504", "endColumns": "87,68,82,69,66,71,71", "endOffsets": "138,207,290,360,427,499,571"}, "to": {"startLines": "65,151,152,154,168,219,220", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5183,13222,13291,13435,14454,18633,18705", "endColumns": "87,68,82,69,66,71,71", "endOffsets": "5266,13286,13369,13500,14516,18700,18772"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7bb9f3beb2453d006dfcb077349e135e\\transformed\\exoplayer-core-2.18.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,262,333,420,491,578,662", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "121,186,257,328,415,486,573,657,738"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9885,9956,10021,10092,10163,10250,10321,10408,10492", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "9951,10016,10087,10158,10245,10316,10403,10487,10568"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f3b883529050a580787eb1fe00d61c7b\\transformed\\exoplayer-ui-2.18.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,484,676,768,858,936,1026,1123,1210,1276,1371,1467,1535,1601,1666,1736,1867,1996,2132,2204,2285,2357,2445,2539,2630,2697,2763,2816,2877,2925,2986,3059,3135,3195,3265,3323,3380,3446,3511,3577,3629,3688,3764,3840", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,91,89,77,89,96,86,65,94,95,67,65,64,69,130,128,135,71,80,71,87,93,90,66,65,52,60,47,60,72,75,59,69,57,56,65,64,65,51,58,75,75,54", "endOffsets": "280,479,671,763,853,931,1021,1118,1205,1271,1366,1462,1530,1596,1661,1731,1862,1991,2127,2199,2280,2352,2440,2534,2625,2692,2758,2811,2872,2920,2981,3054,3130,3190,3260,3318,3375,3441,3506,3572,3624,3683,3759,3835,3890"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,579,7798,7890,7980,8058,8148,8245,8332,8398,8493,8589,8657,8723,8788,8858,8989,9118,9254,9326,9407,9479,9567,9661,9752,9819,10573,10626,10687,10735,10796,10869,10945,11005,11075,11133,11190,11256,11321,11387,11439,11498,11574,11650", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,91,89,77,89,96,86,65,94,95,67,65,64,69,130,128,135,71,80,71,87,93,90,66,65,52,60,47,60,72,75,59,69,57,56,65,64,65,51,58,75,75,54", "endOffsets": "375,574,766,7885,7975,8053,8143,8240,8327,8393,8488,8584,8652,8718,8783,8853,8984,9113,9249,9321,9402,9474,9562,9656,9747,9814,9880,10621,10682,10730,10791,10864,10940,11000,11070,11128,11185,11251,11316,11382,11434,11493,11569,11645,11700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6c6e309b72c7c7f21fbfce9b95a8faf6\\transformed\\core-1.13.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "55,56,57,58,59,60,61,221", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4126,4226,4328,4431,4538,4642,4746,18777", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "4221,4323,4426,4533,4637,4741,4852,18873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8c47125c9a335e989accf2286b6eb952\\transformed\\play-services-basement-18.4.0\\res\\values-ky\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "153", "endOffsets": "348"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "6258", "endColumns": "157", "endOffsets": "6411"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7becb12098085a58be85c10a694bf84d\\transformed\\material-1.12.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,348,433,518,633,743,844,985,1069,1129,1193,1287,1357,1418,1505,1568,1632,1691,1765,1827,1881,1998,2056,2117,2171,2245,2367,2451,2530,2630,2716,2812,2944,3022,3100,3229,3318,3398,3459,3514,3580,3649,3726,3797,3878,3952,4028,4118,4191,4293,4378,4457,4547,4639,4713,4798,4888,4940,5024,5089,5174,5259,5321,5385,5448,5517,5634,5742,5842,5946,6011,6070,6152,6238,6314", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,82,84,84,114,109,100,140,83,59,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,78,99,85,95,131,77,77,128,88,79,60,54,65,68,76,70,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81,85,75,82", "endOffsets": "260,343,428,513,628,738,839,980,1064,1124,1188,1282,1352,1413,1500,1563,1627,1686,1760,1822,1876,1993,2051,2112,2166,2240,2362,2446,2525,2625,2711,2807,2939,3017,3095,3224,3313,3393,3454,3509,3575,3644,3721,3792,3873,3947,4023,4113,4186,4288,4373,4452,4542,4634,4708,4793,4883,4935,5019,5084,5169,5254,5316,5380,5443,5512,5629,5737,5837,5941,6006,6065,6147,6233,6309,6392"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,86,87,139,150,153,155,156,157,158,159,160,161,162,163,164,165,166,167,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,216,217,218", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "771,3648,3731,3816,3901,4016,4857,4958,5099,7674,7734,11705,13152,13374,13505,13592,13655,13719,13778,13852,13914,13968,14085,14143,14204,14258,14332,14521,14605,14684,14784,14870,14966,15098,15176,15254,15383,15472,15552,15613,15668,15734,15803,15880,15951,16032,16106,16182,16272,16345,16447,16532,16611,16701,16793,16867,16952,17042,17094,17178,17243,17328,17413,17475,17539,17602,17671,17788,17896,17996,18100,18165,18224,18388,18474,18550", "endLines": "22,50,51,52,53,54,62,63,64,86,87,139,150,153,155,156,157,158,159,160,161,162,163,164,165,166,167,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,216,217,218", "endColumns": "12,82,84,84,114,109,100,140,83,59,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,78,99,85,95,131,77,77,128,88,79,60,54,65,68,76,70,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81,85,75,82", "endOffsets": "931,3726,3811,3896,4011,4121,4953,5094,5178,7729,7793,11794,13217,13430,13587,13650,13714,13773,13847,13909,13963,14080,14138,14199,14253,14327,14449,14600,14679,14779,14865,14961,15093,15171,15249,15378,15467,15547,15608,15663,15729,15798,15875,15946,16027,16101,16177,16267,16340,16442,16527,16606,16696,16788,16862,16947,17037,17089,17173,17238,17323,17408,17470,17534,17597,17666,17783,17891,17991,18095,18160,18219,18301,18469,18545,18628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bf4e5700f050532bbd59ead7b0c07184\\transformed\\play-services-base-18.5.0\\res\\values-ky\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,440,565,668,809,932,1043,1148,1313,1416,1562,1688,1821,1981,2041,2097", "endColumns": "101,144,124,102,140,122,110,104,164,102,145,125,132,159,59,55,73", "endOffsets": "294,439,564,667,808,931,1042,1147,1312,1415,1561,1687,1820,1980,2040,2096,2170"}, "to": {"startLines": "66,67,68,69,70,71,72,73,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5271,5377,5526,5655,5762,5907,6034,6149,6416,6585,6692,6842,6972,7109,7273,7337,7397", "endColumns": "105,148,128,106,144,126,114,108,168,106,149,129,136,163,63,59,77", "endOffsets": "5372,5521,5650,5757,5902,6029,6144,6253,6580,6687,6837,6967,7104,7268,7332,7392,7470"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\df4af9fc07c95635f45d375fd55e05f1\\transformed\\play-services-wallet-18.1.3\\res\\values-ky\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "80", "endOffsets": "282"}, "to": {"startLines": "222", "startColumns": "4", "startOffsets": "18878", "endColumns": "84", "endOffsets": "18958"}}]}]}