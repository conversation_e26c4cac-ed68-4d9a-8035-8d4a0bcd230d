{"logs": [{"outputFile": "com.postureapp.android.app-mergeDebugResources-67:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f3b883529050a580787eb1fe00d61c7b\\transformed\\exoplayer-ui-2.18.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,502,695,787,879,969,1069,1171,1248,1313,1405,1497,1568,1638,1699,1769,1910,2046,2185,2260,2344,2419,2490,2584,2678,2742,2821,2874,2932,2980,3041,3108,3170,3235,3302,3361,3423,3489,3553,3620,3674,3734,3808,3882", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,91,91,89,99,101,76,64,91,91,70,69,60,69,140,135,138,74,83,74,70,93,93,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "281,497,690,782,874,964,1064,1166,1243,1308,1400,1492,1563,1633,1694,1764,1905,2041,2180,2255,2339,2414,2485,2579,2673,2737,2816,2869,2927,2975,3036,3103,3165,3230,3297,3356,3418,3484,3548,3615,3669,3729,3803,3877,3931"}, "to": {"startLines": "2,11,15,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,597,7911,8003,8095,8185,8285,8387,8464,8529,8621,8713,8784,8854,8915,8985,9126,9262,9401,9476,9560,9635,9706,9800,9894,9958,10747,10800,10858,10906,10967,11034,11096,11161,11228,11287,11349,11415,11479,11546,11600,11660,11734,11808", "endLines": "10,14,18,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "endColumns": "17,12,12,91,91,89,99,101,76,64,91,91,70,69,60,69,140,135,138,74,83,74,70,93,93,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "376,592,785,7998,8090,8180,8280,8382,8459,8524,8616,8708,8779,8849,8910,8980,9121,9257,9396,9471,9555,9630,9701,9795,9889,9953,10032,10795,10853,10901,10962,11029,11091,11156,11223,11282,11344,11410,11474,11541,11595,11655,11729,11803,11857"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7bb9f3beb2453d006dfcb077349e135e\\transformed\\exoplayer-core-2.18.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,195,268,336,416,493,594,687", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "122,190,263,331,411,488,589,682,760"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10037,10109,10177,10250,10318,10398,10475,10576,10669", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "10104,10172,10245,10313,10393,10470,10571,10664,10742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8c47125c9a335e989accf2286b6eb952\\transformed\\play-services-basement-18.4.0\\res\\values-te\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6360", "endColumns": "137", "endOffsets": "6493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6c6e309b72c7c7f21fbfce9b95a8faf6\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "56,57,58,59,60,61,62,235", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4242,4344,4452,4554,4655,4761,4868,20008", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "4339,4447,4549,4650,4756,4863,4987,20104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bf4e5700f050532bbd59ead7b0c07184\\transformed\\play-services-base-18.5.0\\res\\values-te\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,301,451,577,688,821,942,1043,1139,1284,1392,1541,1669,1816,1975,2035,2101", "endColumns": "107,149,125,110,132,120,100,95,144,107,148,127,146,158,59,65,79", "endOffsets": "300,450,576,687,820,941,1042,1138,1283,1391,1540,1668,1815,1974,2034,2100,2180"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5382,5494,5648,5778,5893,6030,6155,6260,6498,6647,6759,6912,7044,7195,7358,7422,7492", "endColumns": "111,153,129,114,136,124,104,99,148,111,152,131,150,162,63,69,83", "endOffsets": "5489,5643,5773,5888,6025,6150,6255,6355,6642,6754,6907,7039,7190,7353,7417,7487,7571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\df4af9fc07c95635f45d375fd55e05f1\\transformed\\play-services-wallet-18.1.3\\res\\values-te\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "77", "endOffsets": "279"}, "to": {"startLines": "240", "startColumns": "4", "startOffsets": "20404", "endColumns": "81", "endOffsets": "20481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7becb12098085a58be85c10a694bf84d\\transformed\\material-1.12.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,276,365,455,541,639,726,830,946,1037,1098,1164,1258,1325,1387,1480,1544,1612,1675,1749,1814,1868,1989,2046,2108,2162,2241,2369,2457,2538,2636,2719,2811,2956,3036,3118,3243,3331,3413,3473,3525,3591,3666,3744,3815,3894,3967,4043,4124,4193,4313,4418,4495,4586,4679,4753,4830,4922,4979,5060,5126,5210,5296,5359,5424,5488,5557,5667,5775,5874,5980,6044,6100,6183,6280,6358", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,88,89,85,97,86,103,115,90,60,65,93,66,61,92,63,67,62,73,64,53,120,56,61,53,78,127,87,80,97,82,91,144,79,81,124,87,81,59,51,65,74,77,70,78,72,75,80,68,119,104,76,90,92,73,76,91,56,80,65,83,85,62,64,63,68,109,107,98,105,63,55,82,96,77,73", "endOffsets": "271,360,450,536,634,721,825,941,1032,1093,1159,1253,1320,1382,1475,1539,1607,1670,1744,1809,1863,1984,2041,2103,2157,2236,2364,2452,2533,2631,2714,2806,2951,3031,3113,3238,3326,3408,3468,3520,3586,3661,3739,3810,3889,3962,4038,4119,4188,4308,4413,4490,4581,4674,4748,4825,4917,4974,5055,5121,5205,5291,5354,5419,5483,5552,5662,5770,5869,5975,6039,6095,6178,6275,6353,6427"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,87,88,140,152,155,157,158,159,160,161,162,163,164,165,166,167,168,169,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,224,225,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "790,3792,3881,3971,4057,4155,4992,5096,5212,7784,7845,11862,13337,13556,13686,13779,13843,13911,13974,14048,14113,14167,14288,14345,14407,14461,14540,14886,14974,15055,15153,15236,15328,15473,15553,15635,15760,15848,15930,15990,16042,16108,16183,16261,16332,16411,16484,16560,16641,16710,16830,16935,17012,17103,17196,17270,17347,17439,17496,17577,17643,17727,17813,17876,17941,18005,18074,18184,18292,18391,18497,18561,18617,19102,19199,19277", "endLines": "22,51,52,53,54,55,63,64,65,87,88,140,152,155,157,158,159,160,161,162,163,164,165,166,167,168,169,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,224,225,226", "endColumns": "12,88,89,85,97,86,103,115,90,60,65,93,66,61,92,63,67,62,73,64,53,120,56,61,53,78,127,87,80,97,82,91,144,79,81,124,87,81,59,51,65,74,77,70,78,72,75,80,68,119,104,76,90,92,73,76,91,56,80,65,83,85,62,64,63,68,109,107,98,105,63,55,82,96,77,73", "endOffsets": "961,3876,3966,4052,4150,4237,5091,5207,5298,7840,7906,11951,13399,13613,13774,13838,13906,13969,14043,14108,14162,14283,14340,14402,14456,14535,14663,14969,15050,15148,15231,15323,15468,15548,15630,15755,15843,15925,15985,16037,16103,16178,16256,16327,16406,16479,16555,16636,16705,16825,16930,17007,17098,17191,17265,17342,17434,17491,17572,17638,17722,17808,17871,17936,18000,18069,18179,18287,18386,18492,18556,18612,18695,19194,19272,19346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ecf58a1d5a3b5cc4faabc13e873181f2\\transformed\\react-android-0.79.5-debug\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,206,278,348,430,498,565,640,716,801,883,954,1035,1115,1198,1284,1372,1450,1526,1601,1692,1764,1843,1912", "endColumns": "71,78,71,69,81,67,66,74,75,84,81,70,80,79,82,85,87,77,75,74,90,71,78,68,74", "endOffsets": "122,201,273,343,425,493,560,635,711,796,878,949,1030,1110,1193,1279,1367,1445,1521,1596,1687,1759,1838,1907,1982"}, "to": {"startLines": "50,66,151,153,154,156,170,171,172,219,220,221,222,227,228,229,230,231,232,233,234,236,237,238,239", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3720,5303,13265,13404,13474,13618,14668,14735,14810,18700,18785,18867,18938,19351,19431,19514,19600,19688,19766,19842,19917,20109,20181,20260,20329", "endColumns": "71,78,71,69,81,67,66,74,75,84,81,70,80,79,82,85,87,77,75,74,90,71,78,68,74", "endOffsets": "3787,5377,13332,13469,13551,13681,14730,14805,14881,18780,18862,18933,19014,19426,19509,19595,19683,19761,19837,19912,20003,20176,20255,20324,20399"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\88b9f6ad7ed87d7d30486cdd2fcbb5f7\\transformed\\biometric-1.1.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,263,382,514,640,790,911,1044,1144,1299,1439", "endColumns": "116,90,118,131,125,149,120,132,99,154,139,132", "endOffsets": "167,258,377,509,635,785,906,1039,1139,1294,1434,1567"}, "to": {"startLines": "85,86,141,142,143,144,145,146,147,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7576,7693,11956,12075,12207,12333,12483,12604,12737,12837,12992,13132", "endColumns": "116,90,118,131,125,149,120,132,99,154,139,132", "endOffsets": "7688,7779,12070,12202,12328,12478,12599,12732,12832,12987,13127,13260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9a9d7ace362c65c6063a55648731b1\\transformed\\appcompat-1.7.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "966,1083,1195,1308,1398,1503,1622,1700,1776,1867,1960,2055,2149,2249,2342,2437,2532,2623,2714,2803,2917,3021,3120,3235,3340,3455,3617,19019", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "1078,1190,1303,1393,1498,1617,1695,1771,1862,1955,2050,2144,2244,2337,2432,2527,2618,2709,2798,2912,3016,3115,3230,3335,3450,3612,3715,19097"}}]}]}