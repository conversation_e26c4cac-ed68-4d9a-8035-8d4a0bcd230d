{"logs": [{"outputFile": "com.postureapp.android.app-mergeDebugResources-67:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ecf58a1d5a3b5cc4faabc13e873181f2\\transformed\\react-android-0.79.5-debug\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,208,280,350,432,500,567,640,719,803,889,958,1035,1116,1192,1273,1354,1430,1505,1580,1666,1736,1812,1886", "endColumns": "75,76,71,69,81,67,66,72,78,83,85,68,76,80,75,80,80,75,74,74,85,69,75,73,78", "endOffsets": "126,203,275,345,427,495,562,635,714,798,884,953,1030,1111,1187,1268,1349,1425,1500,1575,1661,1731,1807,1881,1960"}, "to": {"startLines": "50,66,151,153,154,156,170,171,172,219,220,221,222,227,228,229,230,231,232,233,234,236,237,238,239", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3565,5047,12610,12752,12822,12964,13947,14014,14087,17852,17936,18022,18091,18483,18564,18640,18721,18802,18878,18953,19028,19215,19285,19361,19435", "endColumns": "75,76,71,69,81,67,66,72,78,83,85,68,76,80,75,80,80,75,74,74,85,69,75,73,78", "endOffsets": "3636,5119,12677,12817,12899,13027,14009,14082,14161,17931,18017,18086,18163,18559,18635,18716,18797,18873,18948,19023,19109,19280,19356,19430,19509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6c6e309b72c7c7f21fbfce9b95a8faf6\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "56,57,58,59,60,61,62,235", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4040,4136,4239,4337,4435,4538,4643,19114", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "4131,4234,4332,4430,4533,4638,4750,19210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8c47125c9a335e989accf2286b6eb952\\transformed\\play-services-basement-18.4.0\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6094", "endColumns": "128", "endOffsets": "6218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7bb9f3beb2453d006dfcb077349e135e\\transformed\\exoplayer-core-2.18.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,194,262,330,407,480,571,657", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "126,189,257,325,402,475,566,652,731"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9504,9580,9643,9711,9779,9856,9929,10020,10106", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "9575,9638,9706,9774,9851,9924,10015,10101,10180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\af9a9d7ace362c65c6063a55648731b1\\transformed\\appcompat-1.7.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "936,1041,1134,1242,1327,1429,1539,1617,1694,1785,1878,1969,2063,2163,2256,2351,2445,2536,2627,2708,2811,2909,3007,3110,3216,3317,3470,18168", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "1036,1129,1237,1322,1424,1534,1612,1689,1780,1873,1964,2058,2158,2251,2346,2440,2531,2622,2703,2806,2904,3002,3105,3211,3312,3465,3560,18245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f3b883529050a580787eb1fe00d61c7b\\transformed\\exoplayer-ui-2.18.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,478,656,737,816,895,983,1073,1144,1208,1299,1390,1454,1517,1582,1653,1762,1867,1980,2048,2131,2204,2275,2360,2443,2506,2570,2623,2681,2729,2790,2849,2917,2983,3051,3112,3171,3237,3304,3371,3425,3488,3570,3647", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,78,78,87,89,70,63,90,90,63,62,64,70,108,104,112,67,82,72,70,84,82,62,63,52,57,47,60,58,67,65,67,60,58,65,66,66,53,62,81,76,53", "endOffsets": "278,473,651,732,811,890,978,1068,1139,1203,1294,1385,1449,1512,1577,1648,1757,1862,1975,2043,2126,2199,2270,2355,2438,2501,2565,2618,2676,2724,2785,2844,2912,2978,3046,3107,3166,3232,3299,3366,3420,3483,3565,3642,3696"}, "to": {"startLines": "2,11,15,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,378,573,7590,7671,7750,7829,7917,8007,8078,8142,8233,8324,8388,8451,8516,8587,8696,8801,8914,8982,9065,9138,9209,9294,9377,9440,10185,10238,10296,10344,10405,10464,10532,10598,10666,10727,10786,10852,10919,10986,11040,11103,11185,11262", "endLines": "10,14,18,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "endColumns": "17,12,12,80,78,78,87,89,70,63,90,90,63,62,64,70,108,104,112,67,82,72,70,84,82,62,63,52,57,47,60,58,67,65,67,60,58,65,66,66,53,62,81,76,53", "endOffsets": "373,568,746,7666,7745,7824,7912,8002,8073,8137,8228,8319,8383,8446,8511,8582,8691,8796,8909,8977,9060,9133,9204,9289,9372,9435,9499,10233,10291,10339,10400,10459,10527,10593,10661,10722,10781,10847,10914,10981,11035,11098,11180,11257,11311"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7becb12098085a58be85c10a694bf84d\\transformed\\material-1.12.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,598,684,783,896,976,1044,1114,1204,1274,1334,1421,1487,1552,1613,1677,1738,1792,1893,1954,2014,2068,2138,2249,2336,2413,2500,2582,2663,2806,2885,2967,3099,3191,3269,3323,3376,3442,3512,3590,3661,3741,3813,3891,3960,4029,4127,4209,4297,4390,4484,4558,4627,4722,4774,4857,4925,5010,5098,5160,5224,5287,5357,5457,5553,5650,5743,5801,5858,5935,6017,6092", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,72,71,82,84,85,98,112,79,67,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76,81,74,75", "endOffsets": "280,353,425,508,593,679,778,891,971,1039,1109,1199,1269,1329,1416,1482,1547,1608,1672,1733,1787,1888,1949,2009,2063,2133,2244,2331,2408,2495,2577,2658,2801,2880,2962,3094,3186,3264,3318,3371,3437,3507,3585,3656,3736,3808,3886,3955,4024,4122,4204,4292,4385,4479,4553,4622,4717,4769,4852,4920,5005,5093,5155,5219,5282,5352,5452,5548,5645,5738,5796,5853,5930,6012,6087,6163"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,87,88,140,152,155,157,158,159,160,161,162,163,164,165,166,167,168,169,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,224,225,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "751,3641,3714,3786,3869,3954,4755,4854,4967,7452,7520,11316,12682,12904,13032,13119,13185,13250,13311,13375,13436,13490,13591,13652,13712,13766,13836,14166,14253,14330,14417,14499,14580,14723,14802,14884,15016,15108,15186,15240,15293,15359,15429,15507,15578,15658,15730,15808,15877,15946,16044,16126,16214,16307,16401,16475,16544,16639,16691,16774,16842,16927,17015,17077,17141,17204,17274,17374,17470,17567,17660,17718,17775,18250,18332,18407", "endLines": "22,51,52,53,54,55,63,64,65,87,88,140,152,155,157,158,159,160,161,162,163,164,165,166,167,168,169,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,224,225,226", "endColumns": "12,72,71,82,84,85,98,112,79,67,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76,81,74,75", "endOffsets": "931,3709,3781,3864,3949,4035,4849,4962,5042,7515,7585,11401,12747,12959,13114,13180,13245,13306,13370,13431,13485,13586,13647,13707,13761,13831,13942,14248,14325,14412,14494,14575,14718,14797,14879,15011,15103,15181,15235,15288,15354,15424,15502,15573,15653,15725,15803,15872,15941,16039,16121,16209,16302,16396,16470,16539,16634,16686,16769,16837,16922,17010,17072,17136,17199,17269,17369,17465,17562,17655,17713,17770,17847,18327,18402,18478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bf4e5700f050532bbd59ead7b0c07184\\transformed\\play-services-base-18.5.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5124,5230,5377,5500,5607,5743,5867,5986,6223,6367,6472,6619,6741,6881,7032,7096,7164", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "5225,5372,5495,5602,5738,5862,5981,6089,6362,6467,6614,6736,6876,7027,7091,7159,7243"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\88b9f6ad7ed87d7d30486cdd2fcbb5f7\\transformed\\biometric-1.1.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,259,372,499,625,751,871,995,1090,1219,1348", "endColumns": "104,98,112,126,125,125,119,123,94,128,128,114", "endOffsets": "155,254,367,494,620,746,866,990,1085,1214,1343,1458"}, "to": {"startLines": "85,86,141,142,143,144,145,146,147,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7248,7353,11406,11519,11646,11772,11898,12018,12142,12237,12366,12495", "endColumns": "104,98,112,126,125,125,119,123,94,128,128,114", "endOffsets": "7348,7447,11514,11641,11767,11893,12013,12137,12232,12361,12490,12605"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\df4af9fc07c95635f45d375fd55e05f1\\transformed\\play-services-wallet-18.1.3\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "70", "endOffsets": "272"}, "to": {"startLines": "240", "startColumns": "4", "startOffsets": "19514", "endColumns": "74", "endOffsets": "19584"}}]}]}