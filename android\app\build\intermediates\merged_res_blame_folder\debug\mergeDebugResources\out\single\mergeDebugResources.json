[{"merged": "com.postureapp.android.app-debug-69:/mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "com.postureapp.android.app-main-71:/mipmap-xxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.postureapp.android.app-debug-69:/drawable-xhdpi_splashscreen_logo.png.flat", "source": "com.postureapp.android.app-main-71:/drawable-xhdpi/splashscreen_logo.png"}, {"merged": "com.postureapp.android.app-debug-69:/mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "com.postureapp.android.app-main-71:/mipmap-hdpi/ic_launcher_foreground.webp"}, {"merged": "com.postureapp.android.app-debug-69:/drawable-hdpi_splashscreen_logo.png.flat", "source": "com.postureapp.android.app-main-71:/drawable-hdpi/splashscreen_logo.png"}, {"merged": "com.postureapp.android.app-debug-69:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.postureapp.android.app-main-71:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.postureapp.android.app-debug-69:/drawable-xxhdpi_splashscreen_logo.png.flat", "source": "com.postureapp.android.app-main-71:/drawable-xxhdpi/splashscreen_logo.png"}, {"merged": "com.postureapp.android.app-debug-69:/mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "com.postureapp.android.app-main-71:/mipmap-mdpi/ic_launcher_foreground.webp"}, {"merged": "com.postureapp.android.app-debug-69:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.postureapp.android.app-main-71:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.postureapp.android.app-debug-69:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.postureapp.android.app-main-71:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.postureapp.android.app-debug-69:/drawable-mdpi_splashscreen_logo.png.flat", "source": "com.postureapp.android.app-main-71:/drawable-mdpi/splashscreen_logo.png"}, {"merged": "com.postureapp.android.app-debug-69:/drawable-xxxhdpi_splashscreen_logo.png.flat", "source": "com.postureapp.android.app-main-71:/drawable-xxxhdpi/splashscreen_logo.png"}, {"merged": "com.postureapp.android.app-debug-69:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.postureapp.android.app-main-71:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.postureapp.android.app-debug-69:/mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "com.postureapp.android.app-main-71:/mipmap-xxxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.postureapp.android.app-debug-69:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.postureapp.android.app-main-71:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.postureapp.android.app-debug-69:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.postureapp.android.app-main-71:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.postureapp.android.app-debug-69:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.postureapp.android.app-main-71:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.postureapp.android.app-debug-69:/drawable_ic_launcher_background.xml.flat", "source": "com.postureapp.android.app-main-71:/drawable/ic_launcher_background.xml"}, {"merged": "com.postureapp.android.app-debug-69:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.postureapp.android.app-main-71:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.postureapp.android.app-debug-69:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.postureapp.android.app-main-71:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.postureapp.android.app-debug-69:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.postureapp.android.app-main-71:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.postureapp.android.app-debug-69:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.postureapp.android.app-main-71:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.postureapp.android.app-debug-69:/drawable_rn_edit_text_material.xml.flat", "source": "com.postureapp.android.app-main-71:/drawable/rn_edit_text_material.xml"}, {"merged": "com.postureapp.android.app-debug-69:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.postureapp.android.app-main-71:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.postureapp.android.app-debug-69:/mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "com.postureapp.android.app-main-71:/mipmap-xhdpi/ic_launcher_foreground.webp"}]