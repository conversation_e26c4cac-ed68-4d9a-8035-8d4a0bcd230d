com.postureapp.android.app-tracing-ktx-1.2.0-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\05c532ea6eeed5ba96fff64378302997\transformed\tracing-ktx-1.2.0\res
com.postureapp.android.app-appcompat-resources-1.7.0-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\067eb4768d0977cc253e099ab589c211\transformed\appcompat-resources-1.7.0\res
com.postureapp.android.app-lifecycle-viewmodel-savedstate-2.8.7-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\091b80fe653c9f1df561c97c001a4e70\transformed\lifecycle-viewmodel-savedstate-2.8.7\res
com.postureapp.android.app-activity-ktx-1.8.0-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\0b3cd90608d731515ee5b0c8986ce7d6\transformed\activity-ktx-1.8.0\res
com.postureapp.android.app-lifecycle-livedata-core-ktx-2.8.7-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\0b66e427efb6610e0619341c5c036b44\transformed\lifecycle-livedata-core-ktx-2.8.7\res
com.postureapp.android.app-startup-runtime-1.1.1-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\12f7ec3034c44ad7915028fd2e88ba60\transformed\startup-runtime-1.1.1\res
com.postureapp.android.app-constraintlayout-2.0.1-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\162c641a6ded6de9dcbbe71bb887fa9c\transformed\constraintlayout-2.0.1\res
com.postureapp.android.app-savedstate-1.2.1-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\19f8795450958262f34e41d8151462d1\transformed\savedstate-1.2.1\res
com.postureapp.android.app-lifecycle-viewmodel-release-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\1ad891ef6e31c4f4bb2af2795e226bba\transformed\lifecycle-viewmodel-release\res
com.postureapp.android.app-fragment-1.6.1-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\22d97c11c7e69f31bac4e14a1df1d05a\transformed\fragment-1.6.1\res
com.postureapp.android.app-recyclerview-1.2.1-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\27a9adc9a8992d7c38e6434e9fd5299c\transformed\recyclerview-1.2.1\res
com.postureapp.android.app-play-services-maps-17.0.0-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\29f3e55b4764c3def309e3d61ebb4352\transformed\play-services-maps-17.0.0\res
com.postureapp.android.app-media-1.4.3-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\2e7d5ad471056d27938db5c96ba37f1f\transformed\media-1.4.3\res
com.postureapp.android.app-lifecycle-process-2.8.7-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\2ea4441ccd6266f533a7e0524b0289c3\transformed\lifecycle-process-2.8.7\res
com.postureapp.android.app-drawerlayout-1.1.1-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\34bb67911f8414f1ff69475eb4175853\transformed\drawerlayout-1.1.1\res
com.postureapp.android.app-expo.modules.filesystem-18.1.11-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\35e95e07eb5bf2a9d070da191dbee833\transformed\expo.modules.filesystem-18.1.11\res
com.postureapp.android.app-core-runtime-2.2.0-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\3b4cadc5d581274a383a17f34b96bacf\transformed\core-runtime-2.2.0\res
com.postureapp.android.app-firebase-messaging-24.0.1-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\3e2ea98ccde974f562b3bd746689d36e\transformed\firebase-messaging-24.0.1\res
com.postureapp.android.app-lifecycle-viewmodel-ktx-2.8.7-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\408d1821dac29060484e0b248e6ff11e\transformed\lifecycle-viewmodel-ktx-2.8.7\res
com.postureapp.android.app-profileinstaller-1.3.1-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\44c936327e19f1139e00844984689fd4\transformed\profileinstaller-1.3.1\res
com.postureapp.android.app-expo.modules.av-15.1.7-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\4a39f7efb14b41f5e8be270e67699e2b\transformed\expo.modules.av-15.1.7\res
com.postureapp.android.app-core-ktx-1.13.1-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\4bae3fed21d5f02d6bf43c03532e29f6\transformed\core-ktx-1.13.1\res
com.postureapp.android.app-expo.modules.securestore-14.2.4-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\4d363a7f2bcc15fba48ffee0ec1cc12f\transformed\expo.modules.securestore-14.2.4\res
com.postureapp.android.app-fragment-ktx-1.6.1-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\506e255793c8bac2342ad449777733c1\transformed\fragment-ktx-1.6.1\res
com.postureapp.android.app-autofill-1.1.0-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\50fb524026069718687c4ab652c29053\transformed\autofill-1.1.0\res
com.postureapp.android.app-lifecycle-livedata-core-2.8.7-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\53f7df78aac3d1e91893dd1269c6785c\transformed\lifecycle-livedata-core-2.8.7\res
com.postureapp.android.app-cardview-1.0.0-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\5745f858b74ff0a2f8e17d4b26e19812\transformed\cardview-1.0.0\res
com.postureapp.android.app-lifecycle-runtime-release-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\58859b5cac2ae4b77f8a65d7163120db\transformed\lifecycle-runtime-release\res
com.postureapp.android.app-emoji2-1.3.0-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\5895da00ec1caed18a5ab5e37ba29377\transformed\emoji2-1.3.0\res
com.postureapp.android.app-camera-core-1.5.0-alpha03-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\59f10fe347273b10e3d1116168c9dc0e\transformed\camera-core-1.5.0-alpha03\res
com.postureapp.android.app-tracing-1.2.0-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\5e0f9e6bf46b1bd6aeea3c4a7ba42299\transformed\tracing-1.2.0\res
com.postureapp.android.app-camera-view-1.5.0-alpha03-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\65943bdf86080147087f4b6871084d58\transformed\camera-view-1.5.0-alpha03\res
com.postureapp.android.app-viewpager2-1.0.0-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\65ba2f9fa3c9fe83e5152e0f7195116c\transformed\viewpager2-1.0.0\res
com.postureapp.android.app-core-1.13.1-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\6c6e309b72c7c7f21fbfce9b95a8faf6\transformed\core-1.13.1\res
com.postureapp.android.app-savedstate-ktx-1.2.1-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\6d8784d24425b9166c30f1a4f5571e15\transformed\savedstate-ktx-1.2.1\res
com.postureapp.android.app-exoplayer-core-2.18.1-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\7bb9f3beb2453d006dfcb077349e135e\transformed\exoplayer-core-2.18.1\res
com.postureapp.android.app-material-1.12.0-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\7becb12098085a58be85c10a694bf84d\transformed\material-1.12.0\res
com.postureapp.android.app-expo.modules.systemui-5.0.11-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\7e2191088e38e2cef2b99f9020e16edd\transformed\expo.modules.systemui-5.0.11\res
com.postureapp.android.app-camera-lifecycle-1.5.0-alpha03-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\86103d29463ec34fb5274082b54be7fc\transformed\camera-lifecycle-1.5.0-alpha03\res
com.postureapp.android.app-BlurView-version-2.0.6-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\87e4c57220eac6c2a196044df99f723d\transformed\BlurView-version-2.0.6\res
com.postureapp.android.app-biometric-1.1.0-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\88b9f6ad7ed87d7d30486cdd2fcbb5f7\transformed\biometric-1.1.0\res
com.postureapp.android.app-expo.modules.localization-16.1.6-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae5b0c0e376223def0f320b1c2ac912\transformed\expo.modules.localization-16.1.6\res
com.postureapp.android.app-play-services-basement-18.4.0-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\8c47125c9a335e989accf2286b6eb952\transformed\play-services-basement-18.4.0\res
com.postureapp.android.app-drawee-3.6.0-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\90554224eff49d6253bc7f46090a200f\transformed\drawee-3.6.0\res
com.postureapp.android.app-transition-1.5.0-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\925e579c481c06349ca2c1bdadbdec35\transformed\transition-1.5.0\res
com.postureapp.android.app-annotation-experimental-1.4.1-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\9478ca0554caeea9ae571964e0cb2ca6\transformed\annotation-experimental-1.4.1\res
com.postureapp.android.app-coordinatorlayout-1.2.0-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\94f09ae075da36273dd5b0f48955e034\transformed\coordinatorlayout-1.2.0\res
com.postureapp.android.app-lifecycle-runtime-ktx-release-47 C:\Users\<USER>\.gradle\caches\8.13\transforms\9ac8ab4e17a727eb8d4569d4daf8bf21\transformed\lifecycle-runtime-ktx-release\res
com.postureapp.android.app-expo.modules.notifications-0.31.4-48 C:\Users\<USER>\.gradle\caches\8.13\transforms\9d367840efb037b167b0f4cbb6c6cca0\transformed\expo.modules.notifications-0.31.4\res
com.postureapp.android.app-appcompat-1.7.0-49 C:\Users\<USER>\.gradle\caches\8.13\transforms\af9a9d7ace362c65c6063a55648731b1\transformed\appcompat-1.7.0\res
com.postureapp.android.app-standard-core-1.6.54-50 C:\Users\<USER>\.gradle\caches\8.13\transforms\afd8dad723d6ccf409eb9086652f6d8f\transformed\standard-core-1.6.54\res
com.postureapp.android.app-play-services-auth-21.1.0-51 C:\Users\<USER>\.gradle\caches\8.13\transforms\bb41df52539149d398b42f1e722f5bdd\transformed\play-services-auth-21.1.0\res
com.postureapp.android.app-play-services-base-18.5.0-52 C:\Users\<USER>\.gradle\caches\8.13\transforms\bf4e5700f050532bbd59ead7b0c07184\transformed\play-services-base-18.5.0\res
com.postureapp.android.app-camera-extensions-1.5.0-alpha03-53 C:\Users\<USER>\.gradle\caches\8.13\transforms\bf8b5107ff437249fa41919294d3c2e3\transformed\camera-extensions-1.5.0-alpha03\res
com.postureapp.android.app-swiperefreshlayout-1.1.0-54 C:\Users\<USER>\.gradle\caches\8.13\transforms\c3179e0f1b67cb33410fa674edb5496c\transformed\swiperefreshlayout-1.1.0\res
com.postureapp.android.app-activity-1.8.0-55 C:\Users\<USER>\.gradle\caches\8.13\transforms\ce37a28011a1b60b4e3d132dbd8b1975\transformed\activity-1.8.0\res
com.postureapp.android.app-firebase-common-21.0.0-56 C:\Users\<USER>\.gradle\caches\8.13\transforms\d860c615243dbf55873ae6448fc529e5\transformed\firebase-common-21.0.0\res
com.postureapp.android.app-play-services-wallet-18.1.3-57 C:\Users\<USER>\.gradle\caches\8.13\transforms\df4af9fc07c95635f45d375fd55e05f1\transformed\play-services-wallet-18.1.3\res
com.postureapp.android.app-camera-video-1.5.0-alpha03-58 C:\Users\<USER>\.gradle\caches\8.13\transforms\e2de6603824d57df616cf2bd3bf2baea\transformed\camera-video-1.5.0-alpha03\res
com.postureapp.android.app-lifecycle-livedata-2.8.7-59 C:\Users\<USER>\.gradle\caches\8.13\transforms\e4e0d321cb596a615bb0b6b5cfd607ca\transformed\lifecycle-livedata-2.8.7\res
com.postureapp.android.app-camera-camera2-1.5.0-alpha03-60 C:\Users\<USER>\.gradle\caches\8.13\transforms\e9579ce2f304c1543905cbc2d494490a\transformed\camera-camera2-1.5.0-alpha03\res
com.postureapp.android.app-emoji2-views-helper-1.3.0-61 C:\Users\<USER>\.gradle\caches\8.13\transforms\ec1853d0b0976e32a4f952e316c0c604\transformed\emoji2-views-helper-1.3.0\res
com.postureapp.android.app-react-android-0.79.5-debug-62 C:\Users\<USER>\.gradle\caches\8.13\transforms\ecf58a1d5a3b5cc4faabc13e873181f2\transformed\react-android-0.79.5-debug\res
com.postureapp.android.app-exoplayer-ui-2.18.1-63 C:\Users\<USER>\.gradle\caches\8.13\transforms\f3b883529050a580787eb1fe00d61c7b\transformed\exoplayer-ui-2.18.1\res
com.postureapp.android.app-camera-mlkit-vision-1.5.0-alpha03-64 C:\Users\<USER>\.gradle\caches\8.13\transforms\f621cfe3e6d4244acf32896457617e73\transformed\camera-mlkit-vision-1.5.0-alpha03\res
com.postureapp.android.app-pngs-65 D:\PostureApp\android\app\build\generated\res\pngs\debug
com.postureapp.android.app-resValues-66 D:\PostureApp\android\app\build\generated\res\resValues\debug
com.postureapp.android.app-packageDebugResources-67 D:\PostureApp\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.postureapp.android.app-packageDebugResources-68 D:\PostureApp\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.postureapp.android.app-debug-69 D:\PostureApp\android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.postureapp.android.app-debug-70 D:\PostureApp\android\app\src\debug\res
com.postureapp.android.app-main-71 D:\PostureApp\android\app\src\main\res
com.postureapp.android.app-debug-72 D:\PostureApp\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\debug\packageDebugResources
com.postureapp.android.app-debug-73 D:\PostureApp\node_modules\@react-native-community\netinfo\android\build\intermediates\packaged_res\debug\packageDebugResources
com.postureapp.android.app-debug-74 D:\PostureApp\node_modules\@shopify\react-native-skia\android\build\intermediates\packaged_res\debug\packageDebugResources
com.postureapp.android.app-debug-75 D:\PostureApp\node_modules\expo-constants\android\build\intermediates\packaged_res\debug\packageDebugResources
com.postureapp.android.app-debug-76 D:\PostureApp\node_modules\expo-modules-core\android\build\intermediates\packaged_res\debug\packageDebugResources
com.postureapp.android.app-debug-77 D:\PostureApp\node_modules\expo\android\build\intermediates\packaged_res\debug\packageDebugResources
com.postureapp.android.app-debug-78 D:\PostureApp\node_modules\react-native-edge-to-edge\android\build\intermediates\packaged_res\debug\packageDebugResources
com.postureapp.android.app-debug-79 D:\PostureApp\node_modules\react-native-fs\android\build\intermediates\packaged_res\debug\packageDebugResources
com.postureapp.android.app-debug-80 D:\PostureApp\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\debug\packageDebugResources
com.postureapp.android.app-debug-81 D:\PostureApp\node_modules\react-native-razorpay\android\build\intermediates\packaged_res\debug\packageDebugResources
com.postureapp.android.app-debug-82 D:\PostureApp\node_modules\react-native-reanimated\android\build\intermediates\packaged_res\debug\packageDebugResources
com.postureapp.android.app-debug-83 D:\PostureApp\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug\packageDebugResources
com.postureapp.android.app-debug-84 D:\PostureApp\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug\packageDebugResources
com.postureapp.android.app-debug-85 D:\PostureApp\node_modules\react-native-svg\android\build\intermediates\packaged_res\debug\packageDebugResources
com.postureapp.android.app-debug-86 D:\PostureApp\node_modules\react-native-vision-camera\android\build\intermediates\packaged_res\debug\packageDebugResources
com.postureapp.android.app-debug-87 D:\PostureApp\node_modules\react-native-worklets-core\android\build\intermediates\packaged_res\debug\packageDebugResources
