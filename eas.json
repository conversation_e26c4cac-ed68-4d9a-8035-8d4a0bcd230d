{"cli": {"version": ">= 5.9.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "ios": {"resourceClass": "m-medium"}, "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}}, "preview": {"distribution": "internal", "ios": {"resourceClass": "m-medium", "simulator": true}, "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "production": {"ios": {"resourceClass": "m-medium", "autoIncrement": true}, "android": {"buildType": "aab", "gradleCommand": ":app:bundleRelease", "autoIncrement": true}, "env": {"NODE_ENV": "production"}}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "**********", "appleTeamId": "ABCDEFGHIJ"}, "android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "production"}}}}