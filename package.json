{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "AI-powered posture improvement app with real-time pose detection and personalized wellness recommendations", "main": "index.ts", "keywords": ["posture", "wellness", "health", "AI", "pose-detection", "yoga", "ergonomics"], "author": "PostureApp Team", "license": "MIT", "scripts": {"start": "expo start", "start:clear": "expo start --clear", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "prebuild": "expo prebuild", "prebuild:clean": "expo prebuild --clean", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all", "build:preview": "eas build --profile preview", "build:production": "eas build --profile production", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "update": "eas update", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit", "test": "jest", "prepare-release": "npm run type-check && npm run lint && npm run build:production"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@mediapipe/camera_utils": "^0.3.**********", "@mediapipe/drawing_utils": "^0.3.**********", "@mediapipe/pose": "^0.5.**********", "@mediapipe/tasks-vision": "^0.10.22-rc.20250304", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.4.6", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.7", "@shopify/react-native-skia": "^2.0.0-next.4", "@supabase/supabase-js": "^2.56.0", "@types/three": "^0.179.0", "expo": "~53.0.22", "expo-av": "^15.1.7", "expo-blur": "^14.1.5", "expo-camera": "^16.1.11", "expo-constants": "^17.1.7", "expo-device": "^7.1.4", "expo-font": "~13.3.2", "expo-gl": "^15.1.7", "expo-gl-cpp": "^11.4.0", "expo-haptics": "^14.1.4", "expo-linear-gradient": "^14.1.5", "expo-localization": "^16.1.6", "expo-notifications": "^0.31.4", "expo-secure-store": "~14.2.4", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.11", "i18next": "^25.4.2", "lucide-react-native": "^0.542.0", "react": "19.0.0", "react-i18next": "^15.7.2", "react-native": "^0.79.5", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.24.0", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-vision-camera": "^4.7.1", "react-native-worklets-core": "^1.6.2", "three": "^0.166.1", "zod": "^4.1.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.28.3", "@babel/preset-typescript": "^7.27.1", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "jest": "^30.1.2", "jest-environment-jsdom": "^30.1.2", "jest-expo": "^53.0.9", "react-native-testing-library": "^2.2.0", "typescript": "~5.8.3"}, "private": true}