import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  Dimensions,
  StyleSheet,
  Modal,
  TouchableWithoutFeedback,
  Platform,
  AccessibilityInfo,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const isTablet = screenWidth > 768;
const isSmallScreen = screenWidth < 375;

export interface AlertButton {
  text: string;
  onPress?: () => void;
  style?: 'default' | 'cancel' | 'destructive';
}

export interface CustomAlertProps {
  visible: boolean;
  title?: string;
  message: string;
  buttons?: AlertButton[];
  type?: 'info' | 'success' | 'warning' | 'error';
  onDismiss?: () => void;
  backdropDismiss?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number;
}

const CustomAlert: React.FC<CustomAlertProps> = ({
  visible,
  title,
  message,
  buttons = [{ text: 'OK' }],
  type = 'info',
  onDismiss,
  backdropDismiss = true,
  autoHide = false,
  autoHideDelay = 3000,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    if (visible) {
      // Announce to screen readers
      AccessibilityInfo.announceForAccessibility(
        `Alert: ${title ? `${title}. ` : ''}${message}`
      );

      // Show animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();

      // Auto hide if enabled
      if (autoHide) {
        const timer = setTimeout(() => {
          handleDismiss();
        }, autoHideDelay);
        return () => clearTimeout(timer);
      }
    } else {
      // Hide animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 50,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, autoHide, autoHideDelay, title, message]);

  const handleDismiss = () => {
    if (onDismiss) {
      onDismiss();
    }
  };

  const handleBackdropPress = () => {
    if (backdropDismiss) {
      handleDismiss();
    }
  };

  const getAlertConfig = () => {
    switch (type) {
      case 'success':
        return {
          icon: 'checkmark-circle' as const,
          iconColor: '#10B981',
          borderColor: '#10B981',
        };
      case 'warning':
        return {
          icon: 'warning' as const,
          iconColor: '#F59E0B',
          borderColor: '#F59E0B',
        };
      case 'error':
        return {
          icon: 'close-circle' as const,
          iconColor: '#EF4444',
          borderColor: '#EF4444',
        };
      default:
        return {
          icon: 'information-circle' as const,
          iconColor: '#3B82F6',
          borderColor: '#3B82F6',
        };
    }
  };

  const config = getAlertConfig();

  const renderButton = (button: AlertButton, index: number) => {
    const isDestructive = button.style === 'destructive';
    const isCancel = button.style === 'cancel';
    const isLast = index === buttons.length - 1;

    return (
      <TouchableOpacity
        key={index}
        style={[
          styles.button,
          isDestructive && styles.destructiveButton,
          isCancel && styles.cancelButton,
          !isLast && buttons.length > 1 && styles.buttonBorder,
          buttons.length === 1 && styles.singleButton,
        ]}
        onPress={() => {
          button.onPress?.();
          handleDismiss();
        }}
        activeOpacity={0.7}
        accessible={true}
        accessibilityRole="button"
        accessibilityLabel={button.text}
        accessibilityHint={
          isDestructive ? 'Destructive action' :
          isCancel ? 'Cancel action' :
          'Confirm action'
        }
      >
        <Text
          style={[
            styles.buttonText,
            isDestructive && styles.destructiveButtonText,
            isCancel && styles.cancelButtonText,
            isSmallScreen && styles.smallScreenButtonText,
          ]}
          accessible={false}
        >
          {button.text}
        </Text>
      </TouchableOpacity>
    );
  };

  if (!visible) return null;

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      statusBarTranslucent
      onRequestClose={handleDismiss}
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View style={styles.overlay}>
          <Animated.View
            style={[
              styles.backdrop,
              {
                opacity: fadeAnim,
              },
            ]}
          />
          
          <TouchableWithoutFeedback>
            <Animated.View
              style={[
                styles.alertContainer,
                {
                  opacity: fadeAnim,
                  transform: [
                    { scale: scaleAnim },
                    { translateY: slideAnim },
                  ],
                },
              ]}
            >
              {Platform.OS === 'ios' ? (
                <BlurView intensity={95} style={styles.blurContainer}>
                  <View style={[styles.alertContent, { borderColor: config.borderColor }]}>
                    {title && (
                      <View style={styles.titleContainer}>
                        <Ionicons
                          name={config.icon}
                          size={24}
                          color={config.iconColor}
                          style={styles.titleIcon}
                        />
                        <Text style={styles.title}>{title}</Text>
                      </View>
                    )}
                    
                    <Text style={styles.message}>{message}</Text>
                    
                    <View style={styles.buttonsContainer}>
                      {buttons.map((button, index) => renderButton(button, index))}
                    </View>
                  </View>
                </BlurView>
              ) : (
                <View
                  style={[
                    styles.alertContent,
                    { borderColor: config.borderColor },
                    isTablet && styles.tabletAlert,
                    isSmallScreen && styles.smallScreenAlert,
                  ]}
                  accessible={true}
                  accessibilityRole="alert"
                  accessibilityLabel={`Alert: ${title ? `${title}. ` : ''}${message}`}
                >
                  {title && (
                    <View style={styles.titleContainer}>
                      <Ionicons
                        name={config.icon}
                        size={isSmallScreen ? 20 : 24}
                        color={config.iconColor}
                        style={styles.titleIcon}
                        accessible={false}
                      />
                      <Text
                        style={[
                          styles.title,
                          isSmallScreen && styles.smallScreenTitle,
                        ]}
                        accessible={false}
                      >
                        {title}
                      </Text>
                    </View>
                  )}

                  <Text
                    style={[
                      styles.message,
                      isSmallScreen && styles.smallScreenMessage,
                    ]}
                    accessible={false}
                  >
                    {message}
                  </Text>

                  <View style={[
                    styles.buttonsContainer,
                    buttons.length === 1 && styles.singleButtonContainer,
                  ]}>
                    {buttons.map((button, index) => renderButton(button, index))}
                  </View>
                </View>
              )}
            </Animated.View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  },
  alertContainer: {
    width: '100%',
    maxWidth: 320,
  },
  blurContainer: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  alertContent: {
    backgroundColor: '#1F2937',
    borderRadius: 16,
    borderWidth: 1,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  titleIcon: {
    marginRight: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    flex: 1,
  },
  message: {
    fontSize: 16,
    lineHeight: 24,
    color: '#D1D5DB',
    marginBottom: 24,
    textAlign: 'left',
  },
  buttonsContainer: {
    flexDirection: 'row',
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    backgroundColor: '#374151',
  },
  buttonBorder: {
    marginRight: 12,
  },
  cancelButton: {
    backgroundColor: '#4B5563',
  },
  destructiveButton: {
    backgroundColor: '#EF4444',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  cancelButtonText: {
    color: '#D1D5DB',
  },
  destructiveButtonText: {
    color: '#FFFFFF',
  },
  // Responsive styles
  tabletAlert: {
    maxWidth: 400,
    padding: 32,
  },
  smallScreenAlert: {
    padding: 20,
    margin: 16,
  },
  smallScreenTitle: {
    fontSize: 16,
  },
  smallScreenMessage: {
    fontSize: 14,
    lineHeight: 20,
  },
  singleButtonContainer: {
    flexDirection: 'column',
  },
  singleButton: {
    marginRight: 0,
  },
  smallScreenButtonText: {
    fontSize: 14,
  },
});

export default CustomAlert;
