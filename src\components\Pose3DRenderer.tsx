import React, { useMemo, useEffect, useRef, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Canvas, Group, Circle, Line, Paint, Skia, vec } from '@shopify/react-native-skia';
import { PoseKeyPoint } from '../types';

interface Pose3DRendererProps {
  landmarks: PoseKeyPoint[];
  worldLandmarks?: PoseKeyPoint[];
  width: number;
  height: number;
  enableDepthVisualization?: boolean;
  showConfidenceColors?: boolean;
  enableAnimation?: boolean;
}

// 3D pose connections for skeleton rendering
const POSE_CONNECTIONS_3D = [
  // Torso
  [11, 12], [11, 23], [12, 24], [23, 24], // Shoulders to hips
  
  // Arms
  [11, 13], [13, 15], [15, 17], [15, 19], [15, 21], // Left arm
  [12, 14], [14, 16], [16, 18], [16, 20], [16, 22], // Right arm
  
  // Legs
  [23, 25], [25, 27], [27, 29], [27, 31], // Left leg
  [24, 26], [26, 28], [28, 30], [28, 32], // Right leg
  
  // Face (simplified)
  [0, 1], [1, 2], [2, 3], [3, 7], // Left eye region
  [0, 4], [4, 5], [5, 6], [6, 8], // Right eye region
];

const Pose3DRenderer: React.FC<Pose3DRendererProps> = ({
  landmarks,
  worldLandmarks,
  width,
  height,
  enableDepthVisualization = true,
  showConfidenceColors = true,
  enableAnimation = true,
}) => {
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const poseGroupRef = useRef<THREE.Group | null>(null);
  const animationFrameRef = useRef<number | undefined>(undefined);

  // Initialize Three.js scene
  const initializeScene = (gl: WebGLRenderingContext) => {
    // Create renderer
    const renderer = new THREE.WebGLRenderer({
      canvas: {
        width,
        height,
        style: {},
        addEventListener: () => {},
        removeEventListener: () => {},
        clientHeight: height,
        clientWidth: width,
        getContext: () => gl,
      } as any,
      context: gl,
    });
    renderer.setSize(width, height);
    renderer.setClearColor(0x000000, 0); // Transparent background
    rendererRef.current = renderer;

    // Create scene
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // Create camera
    const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
    camera.position.set(0, 0, 2);
    camera.lookAt(0, 0, 0);
    cameraRef.current = camera;

    // Create pose group
    const poseGroup = new THREE.Group();
    scene.add(poseGroup);
    poseGroupRef.current = poseGroup;
  };

  // Convert landmarks to 3D positions
  const convertTo3DPositions = (landmarks: PoseKeyPoint[]): THREE.Vector3[] => {
    return landmarks.map(landmark => {
      // Convert normalized coordinates to 3D space
      const x = (landmark.x - 0.5) * 2; // -1 to 1
      const y = -(landmark.y - 0.5) * 2; // -1 to 1, flipped for 3D
      const z = landmark.z || 0;

      return new THREE.Vector3(x, y, z);
    });
  };

  // Get color based on confidence
  const getConfidenceColor = (visibility: number): number => {
    if (visibility > 0.8) return 0x00ff00; // Green
    if (visibility > 0.6) return 0xffff00; // Yellow
    if (visibility > 0.4) return 0xff8800; // Orange
    return 0xff0000; // Red
  };

  // Create 3D skeleton
  const create3DSkeleton = (positions: THREE.Vector3[]): void => {
    if (!poseGroupRef.current) return;

    // Clear previous skeleton
    poseGroupRef.current.clear();

    // Create connections
    POSE_CONNECTIONS_3D.forEach(([startIdx, endIdx]) => {
      const start = positions[startIdx];
      const end = positions[endIdx];

      if (!start || !end) return;

      const startLandmark = landmarks[startIdx];
      const endLandmark = landmarks[endIdx];

      // Check visibility
      const startVisibility = startLandmark?.visibility || 0;
      const endVisibility = endLandmark?.visibility || 0;

      if (startVisibility < 0.5 || endVisibility < 0.5) return;

      // Create line geometry
      const geometry = new THREE.BufferGeometry().setFromPoints([start, end]);

      // Choose color based on confidence
      const avgVisibility = (startVisibility + endVisibility) / 2;
      const color = showConfidenceColors ? getConfidenceColor(avgVisibility) : 0xff6b35;

      const material = new THREE.LineBasicMaterial({
        color,
        transparent: true,
        opacity: avgVisibility,
      });

      const line = new THREE.Line(geometry, material);
      poseGroupRef.current?.add(line);
    });

    // Create landmark spheres
    positions.forEach((position, index) => {
      const landmark = landmarks[index];
      const visibility = landmark?.visibility || 0;
      
      if (visibility < 0.5) return;

      const geometry = new THREE.SphereGeometry(0.02, 8, 8);
      const color = showConfidenceColors ? getConfidenceColor(visibility) : 0xff6b35;

      const material = new THREE.MeshBasicMaterial({
        color,
        transparent: true,
        opacity: visibility,
      });

      const sphere = new THREE.Mesh(geometry, material);
      sphere.position.copy(position);
      
      // Add depth-based scaling
      if (enableDepthVisualization) {
        const depthScale = 1 + (position.z * 0.3);
        sphere.scale.setScalar(depthScale);
      }
      
      poseGroupRef.current?.add(sphere);
    });
  };

  // Animation loop
  const animate = () => {
    if (!rendererRef.current || !sceneRef.current || !cameraRef.current) return;

    // Render the scene
    rendererRef.current.render(sceneRef.current, cameraRef.current);
    
    if (enableAnimation) {
      animationFrameRef.current = requestAnimationFrame(animate);
    }
  };

  // Update pose visualization when landmarks change
  useEffect(() => {
    if (landmarks.length === 0) return;

    const positions = worldLandmarks 
      ? convertTo3DPositions(worldLandmarks)
      : convertTo3DPositions(landmarks);
    
    create3DSkeleton(positions);
    
    // Start animation if not already running
    if (enableAnimation && !animationFrameRef.current) {
      animate();
    }
  }, [landmarks, worldLandmarks, showConfidenceColors, enableDepthVisualization]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  return (
    <View style={[styles.container, { width, height }]}>
      {/* GLView temporarily disabled for build fix */}
      {/* <GLView
        style={StyleSheet.absoluteFillObject}
        onContextCreate={initializeScene}
      /> */}
      <View style={StyleSheet.absoluteFillObject} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 5,
  },
});

export default Pose3DRenderer;
