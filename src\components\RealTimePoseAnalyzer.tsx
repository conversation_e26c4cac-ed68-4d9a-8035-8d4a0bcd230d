import React, { useRef, useEffect, useState, useCallback } from 'react';
import { View, StyleSheet, Dimensions, Text, Platform } from 'react-native';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { PoseKeyPoint, ARPoseDetection, PostureAnalysis } from '../types';
import { PoseDetectionService } from '../services/poseDetectionService';
import { realTimePostureService } from '../services/realTimePostureService';
import PoseVisualization3D from './PoseVisualization3D';
// import Pose3DRenderer from './Pose3DRenderer';
import RealTimeFeedback from './RealTimeFeedback';
import { logger } from '../utils/logger';

interface RealTimePoseAnalyzerProps {
  onPoseDetected: (detection: ARPoseDetection) => void;
  onPostureAnalysis: (analysis: PostureAnalysis) => void;
  enableRealTimeAnalysis?: boolean;
  enable3DVisualization?: boolean;
  visualizationMode?: '2d' | '3d' | 'both';
  cameraType?: CameraType;
  width?: number;
  height?: number;
  userId?: string;
}

interface PerformanceStats {
  fps: number;
  processingTime: number;
  confidence: number;
  droppedFrames: number;
  memoryUsage: number;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const RealTimePoseAnalyzer: React.FC<RealTimePoseAnalyzerProps> = ({
  onPoseDetected,
  onPostureAnalysis,
  enableRealTimeAnalysis = true,
  enable3DVisualization = true,
  visualizationMode = 'both',
  cameraType = 'front',
  width = screenWidth,
  height = screenHeight * 0.7,
  userId,
}) => {
  const [permission, requestPermission] = useCameraPermissions();
  const [isInitialized, setIsInitialized] = useState(false);
  const [currentDetection, setCurrentDetection] = useState<ARPoseDetection | null>(null);
  const [currentAnalysis, setCurrentAnalysis] = useState<PostureAnalysis | null>(null);
  const [performanceStats, setPerformanceStats] = useState<PerformanceStats>({
    fps: 0,
    processingTime: 0,
    confidence: 0,
    droppedFrames: 0,
    memoryUsage: 0,
  });

  const cameraRef = useRef<CameraView>(null);
  const poseServiceRef = useRef<PoseDetectionService | null>(null);
  const videoElementRef = useRef<HTMLVideoElement | null>(null);
  const analysisIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const performanceIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize pose detection service
  const initializePoseDetection = useCallback(async () => {
    try {
      poseServiceRef.current = PoseDetectionService.getInstance({
        modelComplexity: 1,
        minDetectionConfidence: 0.7,
        minTrackingConfidence: 0.5,
        targetFPS: 30,
        enableWorldLandmarks: true,
        smoothLandmarks: true,
        debugMode: __DEV__,
      });

      await poseServiceRef.current.initialize();

      // Initialize real-time posture service
      realTimePostureService.startMonitoring();

      setIsInitialized(true);

      logger.info('Real-time pose analyzer initialized', {
        enable3D: enable3DVisualization,
        visualizationMode,
        targetFPS: 30
      }, 'RealTimePoseAnalyzer');

    } catch (error) {
      logger.error('Failed to initialize pose analyzer', error, 'RealTimePoseAnalyzer');
    }
  }, [enable3DVisualization, visualizationMode]);

  // Handle pose detection results
  const handlePoseResults = useCallback((detection: ARPoseDetection) => {
    setCurrentDetection(detection);
    onPoseDetected(detection);

    // Perform real-time posture analysis if enabled
    if (enableRealTimeAnalysis && detection.landmarks.length > 0 && userId) {
      try {
        // Use real-time posture service for enhanced analysis
        const analysis = realTimePostureService.analyzeRealTime(detection, userId);
        if (analysis) {
          setCurrentAnalysis(analysis);
          onPostureAnalysis(analysis);
        }
      } catch (error) {
        logger.error('Real-time posture analysis failed', error, 'RealTimePoseAnalyzer');
      }
    }
  }, [onPoseDetected, onPostureAnalysis, enableRealTimeAnalysis, userId]);

  // Start camera and pose detection
  const startDetection = useCallback(async () => {
    if (!poseServiceRef.current || !isInitialized) {
      await initializePoseDetection();
    }

    try {
      // For web platform, we can use video element directly
      if (Platform.OS === 'web' && videoElementRef.current) {
        await poseServiceRef.current?.startCamera(handlePoseResults, videoElementRef.current);
      } else {
        // For mobile, we'll process camera frames differently
        await poseServiceRef.current?.startCamera(handlePoseResults);
      }

      // Start performance monitoring
      startPerformanceMonitoring();

    } catch (error) {
      logger.error('Failed to start pose detection', error, 'RealTimePoseAnalyzer');
    }
  }, [isInitialized, handlePoseResults]);

  // Stop detection
  const stopDetection = useCallback(() => {
    poseServiceRef.current?.stopCamera();
    
    if (analysisIntervalRef.current) {
      clearInterval(analysisIntervalRef.current);
    }
    
    if (performanceIntervalRef.current) {
      clearInterval(performanceIntervalRef.current);
    }
  }, []);

  // Monitor performance metrics
  const startPerformanceMonitoring = () => {
    performanceIntervalRef.current = setInterval(() => {
      if (poseServiceRef.current) {
        const metrics = poseServiceRef.current.getPerformanceMetrics();
        setPerformanceStats({
          fps: metrics.frameRate,
          processingTime: metrics.averageProcessingTime,
          confidence: currentDetection?.confidence || 0,
          droppedFrames: metrics.droppedFrames,
          memoryUsage: metrics.memoryPeakUsage,
        });
      }
    }, 1000); // Update every second
  };

  // Initialize on mount
  useEffect(() => {
    if (permission?.granted) {
      initializePoseDetection();
    }
  }, [permission, initializePoseDetection]);

  // Start detection when initialized
  useEffect(() => {
    if (isInitialized && permission?.granted) {
      startDetection();
    }

    return () => {
      stopDetection();
    };
  }, [isInitialized, permission, startDetection, stopDetection]);

  // Request camera permission if needed
  useEffect(() => {
    if (!permission?.granted) {
      requestPermission();
    }
  }, [permission, requestPermission]);

  if (!permission?.granted) {
    return (
      <View style={[styles.container, { width, height }]}>
        <Text style={styles.permissionText}>Camera permission required for pose detection</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { width, height }]}>
      {/* Camera View */}
      <CameraView
        ref={cameraRef}
        style={StyleSheet.absoluteFillObject}
        facing={cameraType}
      >
        {/* 2D Pose Visualization */}
        {(visualizationMode === '2d' || visualizationMode === 'both') && currentDetection && (
          <PoseVisualization3D
            landmarks={currentDetection.landmarks}
            worldLandmarks={currentDetection.worldLandmarks}
            confidence={currentDetection.confidence}
            width={width}
            height={height}
            showSkeleton={true}
            showLandmarks={true}
            colorScheme="confidence"
            enableSmoothing={true}
          />
        )}

        {/* 3D Pose Visualization - Temporarily disabled for build fix */}
        {/* {(visualizationMode === '3d' || visualizationMode === 'both') &&
         enable3DVisualization &&
         currentDetection?.worldLandmarks && (
          <Pose3DRenderer
            landmarks={currentDetection.landmarks}
            worldLandmarks={currentDetection.worldLandmarks}
            width={width}
            height={height}
            enableDepthVisualization={true}
            showConfidenceColors={true}
            enableAnimation={true}
          />
        )} */}

        {/* Real-time Feedback Overlay */}
        {enableRealTimeAnalysis && (
          <RealTimeFeedback
            analysis={currentAnalysis}
            isRealTime={true}
            confidence={currentDetection?.confidence || 0}
            processingTime={currentDetection?.processingTime || 0}
            frameRate={currentDetection?.frameRate || 0}
          />
        )}

        {/* Performance Stats Overlay (Debug Mode) */}
        {__DEV__ && (
          <View style={styles.performanceOverlay}>
            <Text style={styles.performanceText}>FPS: {performanceStats.fps.toFixed(1)}</Text>
            <Text style={styles.performanceText}>
              Processing: {performanceStats.processingTime.toFixed(1)}ms
            </Text>
            <Text style={styles.performanceText}>
              Confidence: {(performanceStats.confidence * 100).toFixed(1)}%
            </Text>
            <Text style={styles.performanceText}>
              Dropped: {performanceStats.droppedFrames}
            </Text>
          </View>
        )}
      </CameraView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#000000',
    overflow: 'hidden',
  },
  permissionText: {
    color: '#FFFFFF',
    fontSize: 16,
    textAlign: 'center',
    marginTop: 50,
  },
  performanceOverlay: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 8,
    borderRadius: 8,
  },
  performanceText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontFamily: 'monospace',
  },
});

export default RealTimePoseAnalyzer;
