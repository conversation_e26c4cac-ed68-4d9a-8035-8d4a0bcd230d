import React, { createContext, useContext, useState, ReactNode } from 'react';
import CustomAlert, { AlertButton } from '../components/CustomAlert';

export interface AlertOptions {
  title?: string;
  message: string;
  buttons?: AlertButton[];
  type?: 'info' | 'success' | 'warning' | 'error';
  backdropDismiss?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number;
}

interface AlertContextType {
  showAlert: (options: AlertOptions) => void;
  hideAlert: () => void;
  isVisible: boolean;
}

const AlertContext = createContext<AlertContextType | undefined>(undefined);

export const useAlert = (): AlertContextType => {
  const context = useContext(AlertContext);
  if (!context) {
    throw new Error('useAlert must be used within an AlertProvider');
  }
  return context;
};

interface AlertProviderProps {
  children: ReactNode;
}

export const AlertProvider: React.FC<AlertProviderProps> = ({ children }) => {
  const [alertState, setAlertState] = useState<{
    visible: boolean;
    options: AlertOptions | null;
  }>({
    visible: false,
    options: null,
  });

  const showAlert = (options: AlertOptions) => {
    setAlertState({
      visible: true,
      options,
    });
  };

  const hideAlert = () => {
    setAlertState({
      visible: false,
      options: null,
    });
  };

  const handleDismiss = () => {
    hideAlert();
    alertState.options?.buttons?.find(b => b.style === 'cancel')?.onPress?.();
  };

  return (
    <AlertContext.Provider
      value={{
        showAlert,
        hideAlert,
        isVisible: alertState.visible,
      }}
    >
      {children}
      
      {alertState.options && (
        <CustomAlert
          visible={alertState.visible}
          title={alertState.options.title}
          message={alertState.options.message}
          buttons={alertState.options.buttons}
          type={alertState.options.type}
          onDismiss={handleDismiss}
          backdropDismiss={alertState.options.backdropDismiss}
          autoHide={alertState.options.autoHide}
          autoHideDelay={alertState.options.autoHideDelay}
        />
      )}
    </AlertContext.Provider>
  );
};

// Utility function to replace Alert.alert() calls
export const showCustomAlert = (
  title: string,
  message?: string,
  buttons?: AlertButton[],
  options?: Partial<AlertOptions>
) => {
  // This will be used as a global function
  // Implementation will be provided by the AlertProvider
  console.warn('showCustomAlert called outside of AlertProvider context');
};

// Global alert instance for use outside of React components
let globalAlertContext: AlertContextType | null = null;

export const setGlobalAlertContext = (context: AlertContextType) => {
  globalAlertContext = context;
};

export const globalAlert = {
  alert: (
    title: string,
    message?: string,
    buttons?: AlertButton[],
    options?: Partial<AlertOptions>
  ) => {
    if (globalAlertContext) {
      globalAlertContext.showAlert({
        title: message ? title : undefined,
        message: message || title,
        buttons,
        ...options,
      });
    } else {
      console.warn('Global alert context not available');
    }
  },
};
