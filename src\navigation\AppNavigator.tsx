import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { Platform, View } from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import CustomTabBar from '../components/CustomTabBar';

// Import screens
import OnboardingScreen from '../screens/OnboardingScreen';
import AuthScreen from '../screens/AuthScreen';
import HomeScreen from '../screens/HomeScreen';
import PostureCheckScreen from '../screens/PostureCheckScreen';
import YogaExercisesScreen from '../screens/YogaExercisesScreen';
import YogaSessionScreen from '../screens/YogaSessionScreen';
import ProgressScreen from '../screens/ProgressScreen';
import ProfileScreen from '../screens/ProfileScreen';
import SubscriptionScreen from '../screens/SubscriptionScreen';
import SettingsScreen from '../screens/SettingsScreen';
import FamilyScreen from '../screens/FamilyScreen';
import TermsPrivacyScreen from '../screens/TermsPrivacyScreen';
import QuestionnaireScreen from '../screens/QuestionnaireScreen';
import EnhancedQuestionnaireScreen from '../screens/EnhancedQuestionnaireScreen';
import QuestionnaireResultsScreen from '../screens/QuestionnaireResultsScreen';
import PostureHistoryScreen from '../screens/PostureHistoryScreen';

// Import types
import { RootStackParamList, MainTabParamList } from '../types';

// Create navigators
const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

// Main Tab Navigator
function MainTabNavigator() {
  const { t } = useTranslation();

  return (
    <Tab.Navigator
      tabBar={(props) => <CustomTabBar {...props} />}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          title: t('home.title', 'Home'),
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="PostureCheck"
        component={PostureCheckScreen}
        options={{
          title: t('postureCheck.title', 'Posture Check'),
          headerShown: false,
          tabBarStyle: { display: 'none' },
        }}
      />
      <Tab.Screen
        name="Exercises"
        component={YogaExercisesScreen}
        options={{
          title: t('yoga.title', 'Exercises'),
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="Progress"
        component={ProgressScreen}
        options={{
          title: t('progress.title', 'Progress'),
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: t('profile.title', 'Profile'),
          headerShown: false,
        }}
      />
    </Tab.Navigator>
  );
}

// Main App Navigator
export default function AppNavigator() {
  const { t } = useTranslation();
  const { user, loading } = useAuth();
  const [hasSeenOnboarding, setHasSeenOnboarding] = React.useState(false);
  const [onboardingLoading, setOnboardingLoading] = React.useState(true);

  React.useEffect(() => {
    // Check if user has seen onboarding
    const checkOnboardingStatus = async () => {
      try {
        console.log('Checking onboarding status...');
        const { AppStorage, STORAGE_KEYS } = await import('../utils/storage');
        const onboardingComplete = await AppStorage.getItem(STORAGE_KEYS.ONBOARDING_COMPLETE);
        console.log('Onboarding complete value from storage:', onboardingComplete);
        setHasSeenOnboarding(onboardingComplete === 'true');
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        setHasSeenOnboarding(false);
      } finally {
        setOnboardingLoading(false);
      }
    };

    if (!loading) {
      checkOnboardingStatus();
    }
  }, [loading]);

  // Show loading while checking auth and onboarding status
  if (loading || onboardingLoading) {
    return null; // Or a loading screen
  }

  const getInitialRouteName = () => {
    console.log('Getting initial route name. hasSeenOnboarding:', hasSeenOnboarding);
    if (!hasSeenOnboarding) {
      return 'Onboarding';
    }

    // Check if authenticated user needs questionnaire
    if (user && user.needsOnboardingQuestionnaire && !user.questionnaireCompleted) {
      return 'Questionnaire';
    }

    // Always go to Main app - users can explore without authentication
    return 'Main';
  };

  // Render different navigators based on onboarding status
  if (!hasSeenOnboarding) {
    return (
      <NavigationContainer>
        <Stack.Navigator
          initialRouteName="Onboarding"
          screenOptions={{
            headerStyle: {
              backgroundColor: '#6366F1',
            },
            headerTintColor: '#FFFFFF',
            headerTitleStyle: {
              fontWeight: 'bold',
            },
          }}
        >
          <Stack.Screen
            name="Onboarding"
            component={OnboardingScreen}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="Main"
            component={MainTabNavigator}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="Auth"
            component={AuthScreen}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="YogaSession"
            component={YogaSessionScreen}
            options={{
              title: t('yoga.title', 'Yoga Session'),
              presentation: 'modal',
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="Subscription"
            component={SubscriptionScreen}
            options={{
              title: t('subscription.title', 'Subscription'),
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="Settings"
            component={SettingsScreen}
            options={{
              title: t('profile.preferences', 'Settings'),
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="Family"
            component={FamilyScreen}
            options={{
              title: t('family.title', 'Family'),
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="TermsPrivacy"
            component={TermsPrivacyScreen}
            options={{
              title: 'Terms & Privacy',
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="Questionnaire"
            component={EnhancedQuestionnaireScreen}
            options={{
              title: t('questionnaire.title', 'Questionnaire'),
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="QuestionnaireResults"
            component={QuestionnaireResultsScreen}
            options={{
              title: t('questionnaire.results', 'Results'),
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="PostureHistory"
            component={PostureHistoryScreen}
            options={{
              title: t('profile.postureHistory', 'Posture History'),
              headerShown: false,
            }}
          />
        </Stack.Navigator>
      </NavigationContainer>
    );
  }

  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Main"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#6366F1',
          },
          headerTintColor: '#FFFFFF',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen
          name="Main"
          component={MainTabNavigator}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="Auth"
          component={AuthScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="Onboarding"
          component={OnboardingScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="YogaSession"
          component={YogaSessionScreen}
          options={{
            title: t('yoga.title', 'Yoga Session'),
            presentation: 'modal',
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="Subscription"
          component={SubscriptionScreen}
          options={{
            title: t('subscription.title', 'Subscription'),
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="Settings"
          component={SettingsScreen}
          options={{
            title: t('profile.preferences', 'Settings'),
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="Family"
          component={FamilyScreen}
          options={{
            title: t('family.title', 'Family'),
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="TermsPrivacy"
          component={TermsPrivacyScreen}
          options={{
            title: 'Terms & Privacy',
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="Questionnaire"
          component={EnhancedQuestionnaireScreen}
          options={{
            title: t('questionnaire.title', 'Questionnaire'),
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="QuestionnaireResults"
          component={QuestionnaireResultsScreen}
          options={{
            title: t('questionnaire.results', 'Results'),
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="PostureHistory"
          component={PostureHistoryScreen}
          options={{
            title: t('profile.postureHistory', 'Posture History'),
            headerShown: false,
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}