import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Animated,

  ActivityIndicator,
  StatusBar,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useAuth } from '../contexts/AuthContext';
import { useAlert } from '../contexts/AlertContext';
import { RootStackParamList, QuestionnaireQuestion, QuestionnaireSession, QuestionnaireResponse } from '../types';
import { Theme } from '../constants/designTokens';
import Button from '../components/Button';
import { questionnaireService } from '../services/questionnaireService';
import { logger } from '../utils/logger';

type QuestionnaireScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Questionnaire'>;

interface RouteParams {
  type?: 'onboarding' | 'periodic_update' | 'goal_reassessment' | 'health_check';
  resumeSession?: boolean;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const QuestionnaireScreen: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigation = useNavigation<QuestionnaireScreenNavigationProp>();
  const route = useRoute();
  const { user, loading: authLoading } = useAuth();
  const { showAlert } = useAlert();
  const params = route.params as RouteParams;

  // State management
  const [session, setSession] = useState<QuestionnaireSession | null>(null);
  const [currentQuestion, setCurrentQuestion] = useState<QuestionnaireQuestion | null>(null);
  const [currentAnswer, setCurrentAnswer] = useState<any>(null);
  const [confidence, setConfidence] = useState<number>(5);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [questionIndex, setQuestionIndex] = useState(0);
  const [totalQuestions, setTotalQuestions] = useState(15);
  
  // Animation values
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(30));
  const [progressAnim] = useState(new Animated.Value(0));
  const [scaleAnim] = useState(new Animated.Value(0.95));

  /**
   * Initialize questionnaire session
   */
  useEffect(() => {
    initializeSession();
  }, []);

  /**
   * Animate question entrance with Apple-style spring animation
   */
  useEffect(() => {
    if (currentQuestion) {
      animateQuestionEntrance();
    }
  }, [currentQuestion]);

  /**
   * Update progress animation
   */
  useEffect(() => {
    if (session) {
      Animated.spring(progressAnim, {
        toValue: session.completionPercentage,
        useNativeDriver: false,
        tension: 100,
        friction: 8,
      }).start();
    }
  }, [session?.completionPercentage]);

  const initializeSession = async () => {
    try {
      setLoading(true);
      
      // Wait for auth to complete if still loading
      if (authLoading && !user) {
        setTimeout(() => initializeSession(), 500);
        return;
      }
      
      if (!user) {
        // Navigate back to auth if user is not authenticated
        navigation.replace('Auth');
        return;
      }

      let currentSession: QuestionnaireSession | null = null;

      // Try to resume existing session if requested
      if (params?.resumeSession) {
        currentSession = await questionnaireService.resumeSession(user.id);
      }

      // Start new session if no existing session
      if (!currentSession) {
        currentSession = await questionnaireService.startSession(
          user.id,
          params?.type || 'onboarding'
        );
      }

      setSession(currentSession);
      
      // Get first question
      const nextQuestion = questionnaireService.getNextQuestion(currentSession.responses);
      setCurrentQuestion(nextQuestion);
      setQuestionIndex(currentSession.responses.length);
      
      logger.info('Questionnaire session initialized', {
        sessionId: currentSession.id,
        questionCount: currentSession.responses.length,
      }, 'QuestionnaireScreen');
    } catch (error) {
      logger.error('Failed to initialize questionnaire session', error, 'QuestionnaireScreen');
      showAlert({
        title: t('common.error'),
        message: t('questionnaire.initError', 'Failed to start questionnaire. Please try again.'),
        type: 'error',
        buttons: [{ text: t('common.ok'), onPress: () => navigation.goBack() }],
      });
    } finally {
      setLoading(false);
    }
  };

  const animateQuestionEntrance = () => {
    // Reset animations
    fadeAnim.setValue(0);
    slideAnim.setValue(30);
    scaleAnim.setValue(0.95);

    // Apple-style spring animation
    Animated.parallel([
      Animated.spring(fadeAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
    ]).start();
  };

  const handleAnswerSelect = useCallback(async (answer: any) => {
    if (submitting) return;

    setCurrentAnswer(answer);

    // Auto-advance for single choice questions with haptic feedback
    if (currentQuestion?.type === 'single_choice') {
      // Add haptic feedback for iOS
      if (Platform.OS === 'ios') {
        const Haptics = await import('expo-haptics');
        Haptics.selectionAsync();
      }
      
      setTimeout(() => {
        handleNext();
      }, 300); // Small delay for visual feedback
    }
  }, [currentQuestion, submitting]);

  const handleNext = async () => {
    if (!session || !currentQuestion || currentAnswer === null || submitting) {
      return;
    }

    try {
      setSubmitting(true);

      await questionnaireService.submitResponse(
        session.id,
        currentQuestion.id,
        currentAnswer,
        confidence
      );

      // Update session state locally
      const response: QuestionnaireResponse = {
        questionId: currentQuestion.id,
        answer: currentAnswer,
        timestamp: new Date(),
        confidence,
      };

      const updatedSession = {
        ...session,
        responses: [...session.responses, response],
        completionPercentage: Math.round(
          ((session.responses.length + 1) / totalQuestions) * 100
        ),
      };

      setSession(updatedSession);

      // Check if questionnaire is complete
      if (updatedSession && updatedSession.completionPercentage >= 100) {
        // Complete the session and navigate to results
        navigation.replace('QuestionnaireResults', {
          sessionId: updatedSession.id,
          insights: [],
          recommendations: [],
        });
        return;
      }

      // Get next question
      if (updatedSession) {
        const nextQuestion = questionnaireService.getNextQuestion(updatedSession.responses);
        if (nextQuestion) {
          setCurrentQuestion(nextQuestion);
          setCurrentAnswer(null);
          setConfidence(5);
          setQuestionIndex(updatedSession.responses.length);
        }
      }
    } catch (error) {
      logger.error('Failed to submit questionnaire response', error, 'QuestionnaireScreen');
      showAlert({
        title: t('common.error'),
        message: t('questionnaire.submitError', 'Failed to save your response. Please try again.'),
        type: 'error',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handlePrevious = () => {
    if (questionIndex > 0) {
      // For now, just show a confirmation
      showAlert({
        title: t('questionnaire.goBack'),
        message: t('questionnaire.goBackConfirm', 'Are you sure you want to go back? Your current answer will be lost.'),
        type: 'warning',
        buttons: [
          { text: t('common.cancel'), style: 'cancel' },
          { text: t('common.yes'), onPress: () => {
            setQuestionIndex(prev => Math.max(0, prev - 1));
          }}
        ],
      });
    }
  };

  const handleSkip = () => {
    showAlert({
      title: t('questionnaire.skipTitle', 'Skip Question'),
      message: t('questionnaire.skipMessage', 'Are you sure you want to skip this question? This may affect the accuracy of your personalized recommendations.'),
      type: 'warning',
      buttons: [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('questionnaire.skip'), onPress: () => handleAnswerSelect('skipped') },
      ],
    });
  };

  const renderQuestionContent = () => {
    if (!currentQuestion) return null;

    return (
      <Animated.View
        style={[
          styles.questionContainer,
          {
            opacity: fadeAnim,
            transform: [
              { translateY: slideAnim },
              { scale: scaleAnim },
            ],
          },
        ]}
      >
        {/* Question Header */}
        <View style={styles.questionHeader}>
          <Text style={styles.questionTitle}>
            {currentQuestion.question}
          </Text>
          {currentQuestion.description && (
            <Text style={styles.questionDescription}>
              {currentQuestion.description}
            </Text>
          )}
        </View>

        {/* Question Options */}
        <View style={styles.optionsContainer}>
          {renderQuestionOptions()}
        </View>

        {/* Confidence Slider (for non-boolean questions) */}
        {currentQuestion.type !== 'boolean' && (
          <View style={styles.confidenceContainer}>
            <Text style={styles.confidenceLabel}>
              {t('questionnaire.confidence', 'How confident are you with this answer?')}
            </Text>
            <View style={styles.confidenceScale}>
              {[1, 2, 3, 4, 5].map((value) => (
                <TouchableOpacity
                  key={value}
                  style={[
                    styles.confidenceButton,
                    confidence >= value && styles.confidenceButtonActive,
                  ]}
                  onPress={() => setConfidence(value)}
                >
                  <View style={[
                    styles.confidenceDot,
                    confidence >= value && styles.confidenceDotActive,
                  ]} />
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}
      </Animated.View>
    );
  };

  const renderQuestionOptions = () => {
    if (!currentQuestion) return null;

    switch (currentQuestion.type) {
      case 'single_choice':
      case 'multiple_choice':
        return (
          <View style={styles.choiceOptionsContainer}>
            {currentQuestion.options?.map((option, index) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.optionButton,
                  currentAnswer === option.value && styles.optionButtonSelected,
                ]}
                onPress={() => handleAnswerSelect(option.value)}
                activeOpacity={0.7}
              >
                <View style={styles.optionContent}>
                  <View style={[
                    styles.optionIndicator,
                    currentAnswer === option.value && styles.optionIndicatorSelected,
                  ]}>
                    {currentAnswer === option.value && (
                      <Ionicons 
                        name="checkmark" 
                        size={16} 
                        color={Theme.colors.neutral[0]} 
                      />
                    )}
                  </View>
                  <View style={styles.optionTextContainer}>
                    <Text style={[
                       styles.optionText,
                       currentAnswer === option.value && styles.optionTextSelected,
                     ]}>
                       {option.label}
                     </Text>
                    {option.description && (
                      <Text style={styles.optionDescription}>
                        {option.description}
                      </Text>
                    )}
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        );

      case 'scale':
        return (
          <View style={styles.scaleContainer}>
            <View style={styles.scaleLabels}>
              <Text style={styles.scaleLabel}>
                {currentQuestion.validation?.min || 1}
              </Text>
              <Text style={styles.scaleLabel}>
                {currentQuestion.validation?.max || 5}
              </Text>
            </View>
            <View style={styles.scaleButtons}>
              {Array.from(
                { length: (currentQuestion.validation?.max || 5) - (currentQuestion.validation?.min || 1) + 1 },
                (_, i) => (currentQuestion.validation?.min || 1) + i
              ).map((value) => (
                <TouchableOpacity
                  key={value}
                  style={[
                    styles.scaleButton,
                    currentAnswer === value && styles.scaleButtonSelected,
                  ]}
                  onPress={() => setCurrentAnswer(value)}
                  activeOpacity={0.7}
                >
                  <Text style={[
                    styles.scaleButtonText,
                    currentAnswer === value && styles.scaleButtonTextSelected,
                  ]}>
                    {value}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        );

      case 'boolean':
        return (
          <View style={styles.booleanContainer}>
            {[true, false].map((value) => (
              <TouchableOpacity
                key={value.toString()}
                style={[
                  styles.booleanButton,
                  currentAnswer === value && styles.booleanButtonSelected,
                ]}
                onPress={() => handleAnswerSelect(value)}
                activeOpacity={0.7}
              >
                <View style={[
                  styles.booleanIndicator,
                  currentAnswer === value && styles.booleanIndicatorSelected,
                ]}>
                  <Ionicons 
                    name={value ? 'checkmark' : 'close'} 
                    size={20} 
                    color={currentAnswer === value ? Theme.colors.neutral[0] : Theme.colors.neutral[400]} 
                  />
                </View>
                <Text style={[
                  styles.booleanButtonText,
                  currentAnswer === value && styles.booleanButtonTextSelected,
                ]}>
                  {value ? t('common.yes') : t('common.no')}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        );

      default:
        return null;
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <StatusBar barStyle="dark-content" backgroundColor={Theme.colors.neutral[50]} />
        <View style={styles.loadingContent}>
          <ActivityIndicator size="large" color={Theme.colors.primary[500]} />
          <Text style={styles.loadingText}>
            {t('questionnaire.loading', 'Preparing your personalized questionnaire...')}
          </Text>
        </View>
      </View>
    );
  }

  if (!session || !currentQuestion) {
    return (
      <View style={styles.errorContainer}>
        <StatusBar barStyle="dark-content" backgroundColor={Theme.colors.neutral[50]} />
        <View style={styles.errorContent}>
          <Ionicons name="alert-circle" size={48} color={Theme.colors.error[500]} />
          <Text style={styles.errorText}>
            {t('questionnaire.noQuestions', 'No questions available')}
          </Text>
          <Button
            title={t('common.goBack')}
            onPress={() => navigation.goBack()}
            variant="secondary"
          />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Theme.colors.neutral[0]} />
      
      {/* Header with Blur Effect */}
      <BlurView intensity={95} tint="light" style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
            activeOpacity={0.7}
          >
            <Ionicons name="arrow-back" size={24} color={Theme.colors.neutral[900]} />
          </TouchableOpacity>
          
          <View style={styles.headerTitleContainer}>
            <Text style={styles.headerTitle}>
              {t('questionnaire.title', 'Personal Assessment')}
            </Text>
            <Text style={styles.headerSubtitle}>
              {t('questionnaire.subtitle', 'Help us understand your needs better')}
            </Text>
          </View>
          
          <TouchableOpacity 
            style={styles.skipButton}
            onPress={handleSkip}
            activeOpacity={0.7}
          >
            <Text style={styles.skipButtonText}>
              {t('questionnaire.skip', 'Skip')}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Progress Indicator */}
        <View style={styles.progressContainer}>
          <View style={styles.progressInfo}>
            <Text style={styles.progressText}>
              {t('questionnaire.progress', 'Question {{current}} of {{total}}', {
                current: questionIndex + 1,
                total: totalQuestions,
              })}
            </Text>
            <Text style={styles.progressPercentage}>
              {Math.round(session.completionPercentage)}%
            </Text>
          </View>
          <View style={styles.progressBar}>
            <Animated.View 
              style={[
                styles.progressFill,
                {
                  width: progressAnim.interpolate({
                    inputRange: [0, 100],
                    outputRange: ['0%', '100%'],
                    extrapolate: 'clamp',
                  }),
                },
              ]}
            />
          </View>
        </View>
      </BlurView>

      {/* Question Content */}
      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
        bounces={false}
      >
        <View style={styles.questionCard}>
          {renderQuestionContent()}
        </View>
      </ScrollView>

      {/* Navigation Buttons */}
      {currentQuestion.type !== 'single_choice' && (
        <BlurView intensity={95} tint="light" style={styles.navigationContainer}>
          <View style={styles.navigationContent}>
            <Button
              title={t('common.previous')}
              onPress={handlePrevious}
              variant="ghost"
              disabled={questionIndex === 0}
              style={styles.navButton}
            />
            
            <Button
              title={t('common.next')}
              onPress={handleNext}
              disabled={currentAnswer === null || submitting}
              loading={submitting}
              style={styles.navButton}
            />
          </View>
        </BlurView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Theme.colors.neutral[0],
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: Theme.colors.neutral[50],
  },
  loadingContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing['2xl'],
  },
  loadingText: {
    fontSize: 17,
    fontWeight: '400',
    color: Theme.colors.neutral[600],
    textAlign: 'center',
    marginTop: Theme.spacing.xl,
    lineHeight: 24,
  },
  errorContainer: {
    flex: 1,
    backgroundColor: Theme.colors.neutral[50],
  },
  errorContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing['2xl'],
  },
  errorText: {
    fontSize: 17,
    fontWeight: '400',
    color: Theme.colors.neutral[600],
    textAlign: 'center',
    marginVertical: Theme.spacing.xl,
    lineHeight: 24,
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? 44 : 24,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: Theme.colors.neutral[200],
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.lg,
    paddingVertical: Theme.spacing.md,
  },
  backButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 22,
    backgroundColor: Theme.colors.neutral[100],
  },
  headerTitleContainer: {
    flex: 1,
    marginHorizontal: Theme.spacing.lg,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Theme.colors.neutral[900],
    textAlign: 'center',
    lineHeight: 24,
  },
  headerSubtitle: {
    fontSize: 15,
    fontWeight: '400',
    color: Theme.colors.neutral[600],
    textAlign: 'center',
    marginTop: 2,
    lineHeight: 20,
  },
  skipButton: {
    paddingHorizontal: Theme.spacing.md,
    paddingVertical: Theme.spacing.sm,
    borderRadius: 20,
    backgroundColor: Theme.colors.neutral[100],
  },
  skipButtonText: {
    fontSize: 15,
    fontWeight: '500',
    color: Theme.colors.primary[600],
  },
  progressContainer: {
    paddingHorizontal: Theme.spacing.lg,
    paddingBottom: Theme.spacing.lg,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.md,
  },
  progressText: {
    fontSize: 15,
    fontWeight: '500',
    color: Theme.colors.neutral[700],
  },
  progressPercentage: {
    fontSize: 15,
    fontWeight: '600',
    color: Theme.colors.primary[600],
  },
  progressBar: {
    height: 4,
    backgroundColor: Theme.colors.neutral[200],
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Theme.colors.primary[500],
    borderRadius: 2,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: Theme.spacing.xl,
    paddingBottom: Theme.spacing['3xl'],
  },
  questionCard: {
    backgroundColor: Theme.colors.neutral[0],
    borderRadius: 20,
    padding: Theme.spacing['2xl'],
    shadowColor: Theme.colors.neutral[900],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 16,
    elevation: 2,
  },
  questionContainer: {
    flex: 1,
  },
  questionHeader: {
    marginBottom: Theme.spacing['2xl'],
  },
  questionTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: Theme.colors.neutral[900],
    lineHeight: 32,
    marginBottom: Theme.spacing.md,
  },
  questionDescription: {
    fontSize: 17,
    fontWeight: '400',
    color: Theme.colors.neutral[600],
    lineHeight: 24,
  },
  optionsContainer: {
    marginBottom: Theme.spacing['2xl'],
  },
  choiceOptionsContainer: {
    gap: Theme.spacing.md,
  },
  optionButton: {
    borderRadius: 16,
    borderWidth: 2,
    borderColor: Theme.colors.neutral[200],
    backgroundColor: Theme.colors.neutral[0],
    overflow: 'hidden',
  },
  optionButtonSelected: {
    borderColor: Theme.colors.primary[500],
    backgroundColor: Theme.colors.primary[50],
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: Theme.spacing.lg,
  },
  optionIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Theme.colors.neutral[300],
    backgroundColor: Theme.colors.neutral[0],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Theme.spacing.md,
    marginTop: 2,
  },
  optionIndicatorSelected: {
    borderColor: Theme.colors.primary[500],
    backgroundColor: Theme.colors.primary[500],
  },
  optionTextContainer: {
    flex: 1,
  },
  optionText: {
    fontSize: 17,
    fontWeight: '500',
    color: Theme.colors.neutral[900],
    lineHeight: 24,
  },
  optionTextSelected: {
    color: Theme.colors.primary[700],
  },
  optionDescription: {
    fontSize: 15,
    fontWeight: '400',
    color: Theme.colors.neutral[600],
    lineHeight: 20,
    marginTop: 4,
  },
  scaleContainer: {
    alignItems: 'center',
  },
  scaleLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: Theme.spacing.xl,
    paddingHorizontal: Theme.spacing.md,
  },
  scaleLabel: {
    fontSize: 15,
    fontWeight: '500',
    color: Theme.colors.neutral[600],
  },
  scaleButtons: {
    flexDirection: 'row',
    gap: Theme.spacing.lg,
  },
  scaleButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    borderWidth: 2,
    borderColor: Theme.colors.neutral[200],
    backgroundColor: Theme.colors.neutral[0],
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Theme.colors.neutral[900],
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
  },
  scaleButtonSelected: {
    borderColor: Theme.colors.primary[500],
    backgroundColor: Theme.colors.primary[500],
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 3,
  },
  scaleButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: Theme.colors.neutral[700],
  },
  scaleButtonTextSelected: {
    color: Theme.colors.neutral[0],
  },
  booleanContainer: {
    flexDirection: 'row',
    gap: Theme.spacing.lg,
  },
  booleanButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Theme.spacing.lg,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: Theme.colors.neutral[200],
    backgroundColor: Theme.colors.neutral[0],
  },
  booleanButtonSelected: {
    borderColor: Theme.colors.primary[500],
    backgroundColor: Theme.colors.primary[500],
  },
  booleanIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Theme.colors.neutral[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Theme.spacing.md,
  },
  booleanIndicatorSelected: {
    backgroundColor: Theme.colors.primary[600],
  },
  booleanButtonText: {
    fontSize: 17,
    fontWeight: '500',
    color: Theme.colors.neutral[900],
  },
  booleanButtonTextSelected: {
    color: Theme.colors.neutral[0],
  },
  confidenceContainer: {
    paddingTop: Theme.spacing['2xl'],
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: Theme.colors.neutral[200],
    alignItems: 'center',
  },
  confidenceLabel: {
    fontSize: 17,
    fontWeight: '500',
    color: Theme.colors.neutral[700],
    textAlign: 'center',
    marginBottom: Theme.spacing.xl,
  },
  confidenceScale: {
    flexDirection: 'row',
    gap: Theme.spacing.lg,
  },
  confidenceButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confidenceButtonActive: {
    // Active state handled by dot
  },
  confidenceDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Theme.colors.neutral[300],
  },
  confidenceDotActive: {
    backgroundColor: Theme.colors.primary[500],
  },
  navigationContainer: {
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: Theme.colors.neutral[200],
  },
  navigationContent: {
    flexDirection: 'row',
    paddingHorizontal: Theme.spacing.lg,
    paddingVertical: Theme.spacing.lg,
    paddingBottom: Platform.OS === 'ios' ? Theme.spacing['2xl'] : Theme.spacing.lg,
    gap: Theme.spacing.md,
  },
  navButton: {
    flex: 1,
  },
  nextButton: {
    // Primary button styling handled by Button component
  },
});

export default QuestionnaireScreen;