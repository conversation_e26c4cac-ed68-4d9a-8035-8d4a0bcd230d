import { supabase } from '../lib/supabase';
import { logger } from '../utils/logger';
// import { rateLimiter } from '../utils/validation'; // TODO: Implement validation utils
import { cacheManager } from '../utils/cache';

/**
 * Production-ready API service with authentication, rate limiting,
 * error handling, and comprehensive security measures
 */

export interface ApiConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  enableCaching: boolean;
  enableRateLimiting: boolean;
  maxCacheAge: number;
}

export interface ApiResponse<T = any> {
  data: T;
  status: number;
  message?: string;
  timestamp: string;
  requestId: string;
}

export interface ApiError {
  code: string;
  message: string;
  status: number;
  details?: any;
  timestamp: string;
  requestId: string;
}

class ApiService {
  private static instance: ApiService;
  private config: ApiConfig;
  private requestInterceptors: Array<(config: RequestInit) => RequestInit> = [];
  private responseInterceptors: Array<(response: Response) => Response> = [];
  private pendingRequests: Map<string, AbortController> = new Map();

  private constructor(config: Partial<ApiConfig> = {}) {
    this.config = {
      baseUrl: process.env.EXPO_PUBLIC_API_BASE_URL || 'https://api.postureapp.com',
      timeout: 30000, // 30 seconds
      retryAttempts: 3,
      retryDelay: 1000, // 1 second
      enableCaching: true,
      enableRateLimiting: true,
      maxCacheAge: 5 * 60 * 1000, // 5 minutes
      ...config,
    };

    this.setupDefaultInterceptors();
  }

  public static getInstance(config?: Partial<ApiConfig>): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService(config);
    }
    return ApiService.instance;
  }

  /**
   * Setup default request and response interceptors
   */
  private setupDefaultInterceptors(): void {
    // Request interceptor for authentication (will be handled in applyRequestInterceptors)
    this.addRequestInterceptor((config) => {
      // Auth token will be added in applyRequestInterceptors method
      return config;
    });

    // Request interceptor for common headers
    this.addRequestInterceptor((config) => {
      config.headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Client-Version': '1.0.0',
        'X-Platform': 'react-native',
        ...config.headers,
      };
      return config;
    });
  }

  /**
   * Add request interceptor
   */
  addRequestInterceptor(interceptor: (config: RequestInit) => RequestInit): void {
    this.requestInterceptors.push(interceptor);
  }

  /**
   * Add response interceptor
   */
  addResponseInterceptor(interceptor: (response: Response) => Response): void {
    this.responseInterceptors.push(interceptor);
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check rate limiting
   */
  private async checkRateLimit(endpoint: string): Promise<boolean> {
    if (!this.config.enableRateLimiting) return true;

    const { data: { user } } = await supabase.auth.getUser();
    const userId = user?.id || 'anonymous';
    const key = `${userId}_${endpoint}`;
    
    // Allow 100 requests per minute per endpoint per user
    // TODO: Implement rate limiting
    return true; // rateLimiter.isAllowed(key, 100, 60 * 1000);
  }

  /**
   * Get cache key for request
   */
  private getCacheKey(url: string, options: RequestInit): string {
    const method = options.method || 'GET';
    const body = options.body ? JSON.stringify(options.body) : '';
    return `api_${method}_${url}_${btoa(body).slice(0, 20)}`;
  }

  /**
   * Apply request interceptors
   */
  private async applyRequestInterceptors(config: RequestInit): Promise<RequestInit> {
    let modifiedConfig = { ...config };
    
    // Add authentication token
    const { data: { session } } = await supabase.auth.getSession();
    if (session?.access_token) {
      try {
        modifiedConfig.headers = {
          ...modifiedConfig.headers,
          'Authorization': `Bearer ${session.access_token}`,
        };
      } catch (error) {
        logger.warn('Failed to get auth token', error, 'ApiService');
      }
    }
    
    // Apply other interceptors
    for (const interceptor of this.requestInterceptors) {
      modifiedConfig = interceptor(modifiedConfig);
    }
    
    return modifiedConfig;
  }

  /**
   * Apply response interceptors
   */
  private applyResponseInterceptors(response: Response): Response {
    let modifiedResponse = response;
    
    for (const interceptor of this.responseInterceptors) {
      modifiedResponse = interceptor(modifiedResponse);
    }
    
    return modifiedResponse;
  }

  /**
   * Handle API errors with proper logging and user-friendly messages
   */
  private handleApiError(error: any, requestId: string, endpoint: string): never {
    let apiError: ApiError;

    if (error.name === 'AbortError') {
      apiError = {
        code: 'REQUEST_CANCELLED',
        message: 'Request was cancelled',
        status: 0,
        timestamp: new Date().toISOString(),
        requestId,
      };
    } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
      apiError = {
        code: 'NETWORK_ERROR',
        message: 'Network connection failed. Please check your internet connection.',
        status: 0,
        timestamp: new Date().toISOString(),
        requestId,
      };
    } else if (error.status) {
      // HTTP error
      apiError = {
        code: error.code || `HTTP_${error.status}`,
        message: error.message || this.getStatusMessage(error.status),
        status: error.status,
        details: error.details,
        timestamp: new Date().toISOString(),
        requestId,
      };
    } else {
      // Unknown error
      apiError = {
        code: 'UNKNOWN_ERROR',
        message: 'An unexpected error occurred. Please try again.',
        status: 500,
        details: error.message,
        timestamp: new Date().toISOString(),
        requestId,
      };
    }

    logger.error(`API request failed: ${endpoint}`, apiError, 'ApiService');
    throw apiError;
  }

  /**
   * Get user-friendly status message
   */
  private getStatusMessage(status: number): string {
    const statusMessages: { [key: number]: string } = {
      400: 'Invalid request. Please check your input.',
      401: 'Authentication required. Please log in again.',
      403: 'Access denied. You don\'t have permission for this action.',
      404: 'Resource not found.',
      409: 'Conflict. The resource already exists or is in use.',
      422: 'Invalid data provided. Please check your input.',
      429: 'Too many requests. Please wait before trying again.',
      500: 'Server error. Please try again later.',
      502: 'Service temporarily unavailable. Please try again later.',
      503: 'Service maintenance in progress. Please try again later.',
    };

    return statusMessages[status] || 'An error occurred. Please try again.';
  }

  /**
   * Retry logic with exponential backoff
   */
  private async retryRequest<T>(
    requestFn: () => Promise<T>,
    attempt: number = 1
  ): Promise<T> {
    try {
      return await requestFn();
    } catch (error) {
      if (attempt >= this.config.retryAttempts) {
        throw error;
      }

      // Don't retry on client errors (4xx) except 429 (rate limit)
      if ((error as any)?.status >= 400 && (error as any)?.status < 500 && (error as any)?.status !== 429) {
        throw error;
      }

      const delay = this.config.retryDelay * Math.pow(2, attempt - 1); // Exponential backoff
      logger.warn(`API request failed, retrying in ${delay}ms (attempt ${attempt}/${this.config.retryAttempts})`, 
        { error: (error as any)?.message || 'Unknown error' }, 'ApiService');
      
      await new Promise(resolve => setTimeout(resolve, delay));
      return this.retryRequest(requestFn, attempt + 1);
    }
  }

  /**
   * Core request method with comprehensive error handling
   */
  private async request<T = any>(
    endpoint: string,
    options: RequestInit = {},
    useCache: boolean = true
  ): Promise<ApiResponse<T>> {
    const requestId = this.generateRequestId();
    const url = `${this.config.baseUrl}${endpoint}`;
    const timer = logger.startTimer(`api_${options.method || 'GET'}_${endpoint}`, 'ApiService');

    try {
      // Check rate limiting
      if (!(await this.checkRateLimit(endpoint))) {
        throw {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests. Please wait before trying again.',
          status: 429,
        };
      }

      // Check cache for GET requests
      if (useCache && this.config.enableCaching && (!options.method || options.method === 'GET')) {
        const cacheKey = this.getCacheKey(url, options);
        const cachedResponse = await cacheManager.get<ApiResponse<T>>(cacheKey);
        if (cachedResponse) {
          logger.debug(`Cache hit for ${endpoint}`, { requestId }, 'ApiService');
          return cachedResponse;
        }
      }

      // Setup abort controller for timeout
      const abortController = new AbortController();
      this.pendingRequests.set(requestId, abortController);
      
      const timeoutId = setTimeout(() => {
        abortController.abort();
      }, this.config.timeout);

      // Apply request interceptors
      const modifiedOptions = await this.applyRequestInterceptors({
        ...options,
        signal: abortController.signal,
      });

      // Make the request with retry logic
      const response = await this.retryRequest(async () => {
        const res = await fetch(url, modifiedOptions);
        clearTimeout(timeoutId);
        return this.applyResponseInterceptors(res);
      });

      // Parse response
      let responseData: any;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      // Handle HTTP errors
      if (!response.ok) {
        throw {
          code: responseData.code || `HTTP_${response.status}`,
          message: responseData.message || this.getStatusMessage(response.status),
          status: response.status,
          details: responseData.details,
        };
      }

      const apiResponse: ApiResponse<T> = {
        data: responseData.data || responseData,
        status: response.status,
        message: responseData.message,
        timestamp: new Date().toISOString(),
        requestId,
      };

      // Cache successful GET responses
      if (useCache && this.config.enableCaching && (!options.method || options.method === 'GET')) {
        const cacheKey = this.getCacheKey(url, options);
        await cacheManager.set(cacheKey, apiResponse, this.config.maxCacheAge);
      }

      logger.trackApiCall(endpoint, options.method || 'GET', Date.now() - parseInt(requestId.split('_')[1]), response.status);
      return apiResponse;

    } catch (error) {
      this.handleApiError(error, requestId, endpoint);
    } finally {
      this.pendingRequests.delete(requestId);
      timer();
    }
  }

  // Public HTTP methods

  /**
   * GET request
   */
  async get<T = any>(endpoint: string, params?: Record<string, any>, useCache: boolean = true): Promise<ApiResponse<T>> {
    const url = params ? `${endpoint}?${new URLSearchParams(params).toString()}` : endpoint;
    return this.request<T>(url, { method: 'GET' }, useCache);
  }

  /**
   * POST request
   */
  async post<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    }, false);
  }

  /**
   * PUT request
   */
  async put<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    }, false);
  }

  /**
   * PATCH request
   */
  async patch<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    }, false);
  }

  /**
   * DELETE request
   */
  async delete<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' }, false);
  }

  /**
   * Upload file with progress tracking
   */
  async uploadFile<T = any>(
    endpoint: string,
    file: File | Blob,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<T>> {
    const requestId = this.generateRequestId();
    const url = `${this.config.baseUrl}${endpoint}`;
    const timer = logger.startTimer(`api_upload_${endpoint}`, 'ApiService');

    try {
      // Check rate limiting
      if (!(await this.checkRateLimit(endpoint))) {
        throw {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests. Please wait before trying again.',
          status: 429,
        };
      }

      const formData = new FormData();
      formData.append('file', file);

      // Setup abort controller
      const abortController = new AbortController();
      this.pendingRequests.set(requestId, abortController);

      // Apply request interceptors (excluding content-type for FormData)
      let modifiedOptions: RequestInit = {
        method: 'POST',
        body: formData,
        signal: abortController.signal,
      };

      for (const interceptor of this.requestInterceptors) {
        modifiedOptions = await interceptor(modifiedOptions);
      }

      // Remove content-type header to let browser set it with boundary
      if (modifiedOptions.headers && 'Content-Type' in modifiedOptions.headers) {
        delete (modifiedOptions.headers as any)['Content-Type'];
      }

      const response = await fetch(url, modifiedOptions);
      const responseData = await response.json();

      if (!response.ok) {
        throw {
          code: responseData.code || `HTTP_${response.status}`,
          message: responseData.message || this.getStatusMessage(response.status),
          status: response.status,
          details: responseData.details,
        };
      }

      const apiResponse: ApiResponse<T> = {
        data: responseData.data || responseData,
        status: response.status,
        message: responseData.message,
        timestamp: new Date().toISOString(),
        requestId,
      };

      logger.trackApiCall(endpoint, 'POST', Date.now() - parseInt(requestId.split('_')[1]), response.status);
      return apiResponse;

    } catch (error) {
      this.handleApiError(error, requestId, endpoint);
    } finally {
      this.pendingRequests.delete(requestId);
      timer();
    }
  }

  /**
   * Cancel all pending requests
   */
  cancelAllRequests(): void {
    for (const [requestId, controller] of this.pendingRequests.entries()) {
      controller.abort();
      this.pendingRequests.delete(requestId);
    }
    logger.info('All pending API requests cancelled', undefined, 'ApiService');
  }

  /**
   * Cancel specific request
   */
  cancelRequest(requestId: string): void {
    const controller = this.pendingRequests.get(requestId);
    if (controller) {
      controller.abort();
      this.pendingRequests.delete(requestId);
      logger.info(`API request cancelled: ${requestId}`, undefined, 'ApiService');
    }
  }

  /**
   * Clear API cache
   */
  async clearCache(): Promise<void> {
    await cacheManager.clear();
    logger.info('API cache cleared', undefined, 'ApiService');
  }

  /**
   * Get API health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    latency: number;
    timestamp: string;
  }> {
    const startTime = Date.now();
    
    try {
      await this.get('/health', undefined, false);
      const latency = Date.now() - startTime;
      
      return {
        status: latency < 1000 ? 'healthy' : 'degraded',
        latency,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        latency: Date.now() - startTime,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ApiConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('API service config updated', newConfig, 'ApiService');
  }

  /**
   * Get current configuration
   */
  getConfig(): ApiConfig {
    return { ...this.config };
  }
}

// Export singleton instance
export const apiService = ApiService.getInstance({
  baseUrl: process.env.EXPO_PUBLIC_API_BASE_URL || 'https://api.postureapp.com',
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
  enableCaching: true,
  enableRateLimiting: true,
  maxCacheAge: 5 * 60 * 1000,
});