import { supabase } from '../lib/supabase';
import { AuthService as BaseAuthService } from '../lib/auth';
import { User, UserPreferences } from '../types';

export class AuthService {
  // Register new user
  static async register(
    email: string,
    password: string,
    name: string,
    phone?: string
  ): Promise<User> {
    try {
      const { user, session } = await BaseAuthService.signUp(email, password, name);
      
      if (!user) throw new Error('Registration failed');

      // Create extended user data
      const userData: User = {
        id: user.id,
        email: user.email!,
        name,
        phone,
        subscriptionTier: 'free',
        createdAt: new Date(),
        lastActiveAt: new Date(),
        onboardingCompleted: false,
        questionnaireCompleted: false,
        needsOnboardingQuestionnaire: true,
        preferences: {
          language: 'en',
          notifications: true,
          yogaExperience: 'beginner',
          focusAreas: ['general_wellness'],
        },
      };

      // Update profile with additional data
      await supabase
        .from('profiles')
        .update({
          full_name: name,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      return userData;
    } catch (error: any) {
      throw new Error(error.message || 'Registration failed');
    }
  }

  // Login user
  static async login(email: string, password: string): Promise<User> {
    try {
      const { user, session } = await BaseAuthService.signIn(email, password);
      
      if (!user) throw new Error('Login failed');

      // Update last active time
      await supabase
        .from('profiles')
        .update({
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      // Get user profile
      const profile = await BaseAuthService.getUserProfile(user.id);

      return {
        id: user.id,
        email: user.email!,
        name: profile?.full_name || 'User',
        profileImage: profile?.avatar_url,
        subscriptionTier: 'free',
        createdAt: new Date(profile?.created_at || ''),
        lastActiveAt: new Date(),
        onboardingCompleted: profile?.onboarding_completed || false,
        questionnaireCompleted: profile?.questionnaire_completed || false,
        lastQuestionnaireAt: profile?.last_questionnaire_at ? new Date(profile.last_questionnaire_at) : undefined,
        needsOnboardingQuestionnaire: !profile?.questionnaire_completed,
        preferences: {
          language: 'en',
          notifications: true,
          yogaExperience: 'beginner',
          focusAreas: ['general_wellness'],
        },
      };
    } catch (error: any) {
      throw new Error(error.message || 'Login failed');
    }
  }

  // Google Sign In (placeholder - requires additional setup)
  static async signInWithGoogle(idToken: string): Promise<User> {
    try {
      // Note: Google OAuth requires additional Supabase configuration
      // For now, this is a placeholder implementation
      throw new Error('Google Sign In not yet configured. Please use email/password for now.');
    } catch (error: any) {
      throw new Error(error.message || 'Google sign in failed');
    }
  }

  // Logout user
  static async logout(): Promise<void> {
    try {
      await BaseAuthService.signOut();
    } catch (error: any) {
      throw new Error(error.message || 'Logout failed');
    }
  }

  // Reset password
  static async resetPassword(email: string): Promise<void> {
    try {
      await BaseAuthService.resetPassword(email);
    } catch (error: any) {
      throw new Error(error.message || 'Password reset failed');
    }
  }

  // Email verification
  static async resendEmailVerification(): Promise<void> {
    try {
      const user = await this.getCurrentUser();
      if (!user) throw new Error('No user found');
      
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: user.email!,
      });
      
      if (error) throw error;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to resend verification email');
    }
  }

  // Check if email is verified
  static async isEmailVerified(): Promise<boolean> {
    try {
      const user = await this.getCurrentUser();
      return user?.email_confirmed_at != null;
    } catch (error) {
      return false;
    }
  }

  // Change password (for authenticated users)
  static async changePassword(newPassword: string): Promise<void> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });
      
      if (error) throw error;
    } catch (error: any) {
      throw new Error(error.message || 'Password change failed');
    }
  }

  // Update email (requires verification)
  static async updateEmail(newEmail: string): Promise<void> {
    try {
      const { error } = await supabase.auth.updateUser({
        email: newEmail
      });
      
      if (error) throw error;
    } catch (error: any) {
      throw new Error(error.message || 'Email update failed');
    }
  }

  // Delete account
  static async deleteAccount(): Promise<void> {
    try {
      const user = await this.getCurrentUser();
      if (!user) throw new Error('No user found');

      // Delete user profile first
      await supabase
        .from('profiles')
        .delete()
        .eq('id', user.id);

      // Note: Supabase doesn't have a direct delete user method in the client
      // This would typically be handled by a server-side function
      throw new Error('Account deletion requires contacting support');
    } catch (error: any) {
      throw new Error(error.message || 'Account deletion failed');
    }
  }

  // Session management
  static async refreshSession(): Promise<void> {
    try {
      const { error } = await supabase.auth.refreshSession();
      if (error) throw error;
    } catch (error: any) {
      throw new Error(error.message || 'Session refresh failed');
    }
  }

  // Get session info
  static async getSession() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) throw error;
      return session;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to get session');
    }
  }

  // Get current user
  static async getCurrentUser() {
    return await BaseAuthService.getCurrentUser();
  }

  // Listen to auth state changes
  static onAuthStateChanged(callback: (user: any) => void) {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        callback(session?.user || null);
      }
    );
    return subscription;
  }

  // Update user profile
  static async updateUserProfile(
    userId: string,
    updates: Partial<User>
  ): Promise<void> {
    try {
      await BaseAuthService.updateProfile(userId, {
        full_name: updates.name,
        avatar_url: updates.profileImage,
      });
    } catch (error: any) {
      throw new Error(error.message || 'Profile update failed');
    }
  }

  // Update user preferences (placeholder - you might want to add a preferences table)
  static async updateUserPreferences(
    userId: string,
    preferences: Partial<UserPreferences>
  ): Promise<void> {
    try {
      // For now, store preferences in the profiles table as JSONB
      await supabase
        .from('profiles')
        .update({
          // You might want to add a preferences column to your profiles table
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId);
    } catch (error: any) {
      throw new Error(error.message || 'Preferences update failed');
    }
  }

  // Get user data
  static async getUserData(userId: string): Promise<User | null> {
    try {
      const profile = await BaseAuthService.getUserProfile(userId);
      
      if (!profile) return null;

      return {
        id: profile.id,
        email: profile.email,
        name: profile.full_name || 'User',
        profileImage: profile.avatar_url,
        subscriptionTier: 'free',
        createdAt: new Date(profile.created_at),
        lastActiveAt: new Date(profile.updated_at),
        preferences: {
          language: 'en',
          notifications: true,
          yogaExperience: 'beginner',
          focusAreas: ['general_wellness'],
        },
      };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to get user data');
    }
  }
}