import {
  QuestionnaireQuestion,
  QuestionnaireSession,
  QuestionnaireResponse,
  UserInsight,
  PersonalizedRecommendation,
  QuestionCategory,
  UserPreferences,
  WorkProfile,
  HealthProfile,
  UserGoals,
  LifestyleProfile,
  DeviceUsageProfile,
  PersonalizedSettings,
} from '../types';
import { dataService } from './dataService';
import { logger } from '../utils/logger';
import { cacheManager } from '../utils/cache';

/**
 * Intelligent questionnaire service for personalized user experience
 * Implements adaptive questioning, insight generation, and recommendation engine
 */
export class QuestionnaireService {
  private static instance: QuestionnaireService;
  private questionBank: QuestionnaireQuestion[] = [];
  private currentSession: QuestionnaireSession | null = null;

  private constructor() {
    this.initializeQuestionBank();
  }

  public static getInstance(): QuestionnaireService {
    if (!QuestionnaireService.instance) {
      QuestionnaireService.instance = new QuestionnaireService();
    }
    return QuestionnaireService.instance;
  }

  /**
   * Initialize comprehensive question bank
   */
  private initializeQuestionBank(): void {
    this.questionBank = [
      // Basic Information
      {
        id: 'age_range',
        type: 'single_choice',
        category: 'basic_info',
        question: 'What is your age range?',
        questionHindi: 'आपकी उम्र क्या है?',
        options: [
          { id: '18-25', label: '18-25 years', labelHindi: '18-25 साल', value: '18-25' },
          { id: '26-35', label: '26-35 years', labelHindi: '26-35 साल', value: '26-35' },
          { id: '36-45', label: '36-45 years', labelHindi: '36-45 साल', value: '36-45' },
          { id: '46-55', label: '46-55 years', labelHindi: '46-55 साल', value: '46-55' },
          { id: '56+', label: '56+ years', labelHindi: '56+ साल', value: '56+' },
        ],
        validation: { required: true },
        priority: 'high',
        order: 1,
      },

      // Work Profile Questions
      {
        id: 'work_type',
        type: 'single_choice',
        category: 'work_profile',
        question: 'What best describes your primary work or daily activity?',
        questionHindi: 'आपका मुख्य काम या दैनिक गतिविधि क्या है?',
        options: [
          { id: 'desk_job', label: 'Desk/Office Work', labelHindi: 'डेस्क/ऑफिस का काम', value: 'desk_job', icon: '💻' },
          { id: 'standing_job', label: 'Standing Work', labelHindi: 'खड़े होकर काम', value: 'standing_job', icon: '🧍' },
          { id: 'mixed', label: 'Mixed (Sitting & Standing)', labelHindi: 'मिश्रित (बैठना और खड़ा होना)', value: 'mixed', icon: '🔄' },
          { id: 'physical_labor', label: 'Physical Labor', labelHindi: 'शारीरिक श्रम', value: 'physical_labor', icon: '🏗️' },
          { id: 'student', label: 'Student', labelHindi: 'छात्र', value: 'student', icon: '📚' },
          { id: 'retired', label: 'Retired', labelHindi: 'सेवानिवृत्त', value: 'retired', icon: '🏡' },
          { id: 'other', label: 'Other', labelHindi: 'अन्य', value: 'other', icon: '❓' },
        ],
        validation: { required: true },
        priority: 'high',
        order: 2,
      },

      {
        id: 'work_hours',
        type: 'scale',
        category: 'work_profile',
        question: 'How many hours per day do you spend in your primary work activity?',
        questionHindi: 'आप अपनी मुख्य कार्य गतिविधि में दिन में कितने घंटे बिताते हैं?',
        validation: { required: true, min: 1, max: 16 },
        priority: 'high',
        order: 3,
      },

      {
        id: 'work_environment',
        type: 'single_choice',
        category: 'work_profile',
        question: 'Where do you primarily work?',
        questionHindi: 'आप मुख्यतः कहाँ काम करते हैं?',
        options: [
          { id: 'office', label: 'Traditional Office', labelHindi: 'पारंपरिक कार्यालय', value: 'office', icon: '🏢' },
          { id: 'home', label: 'Home Office', labelHindi: 'घर का कार्यालय', value: 'home', icon: '🏠' },
          { id: 'hybrid', label: 'Hybrid (Office + Home)', labelHindi: 'हाइब्रिड (कार्यालय + घर)', value: 'hybrid', icon: '🔄' },
          { id: 'outdoor', label: 'Outdoor/Field Work', labelHindi: 'बाहरी/फील्ड वर्क', value: 'outdoor', icon: '🌳' },
          { id: 'mobile', label: 'Mobile/Travel', labelHindi: 'मोबाइल/यात्रा', value: 'mobile', icon: '✈️' },
        ],
        conditional: {
          dependsOn: 'work_type',
          condition: 'not_equals',
          value: 'retired'
        },
        validation: { required: true },
        priority: 'high',
        order: 4,
      },

      // Health Profile Questions
      {
        id: 'current_pain_areas',
        type: 'multiple_choice',
        category: 'health_profile',
        question: 'Do you currently experience pain or discomfort in any of these areas?',
        questionHindi: 'क्या आप वर्तमान में इनमें से किसी भी क्षेत्र में दर्द या असुविधा महसूस करते हैं?',
        options: [
          { id: 'neck', label: 'Neck', labelHindi: 'गर्दन', value: 'neck', icon: '🦴' },
          { id: 'shoulders', label: 'Shoulders', labelHindi: 'कंधे', value: 'shoulders', icon: '💪' },
          { id: 'upper_back', label: 'Upper Back', labelHindi: 'ऊपरी पीठ', value: 'upper_back', icon: '🫁' },
          { id: 'lower_back', label: 'Lower Back', labelHindi: 'निचली पीठ', value: 'lower_back', icon: '🦴' },
          { id: 'hips', label: 'Hips', labelHindi: 'कूल्हे', value: 'hips', icon: '🦴' },
          { id: 'head', label: 'Head/Headaches', labelHindi: 'सिर/सिरदर्द', value: 'head', icon: '🧠' },
          { id: 'none', label: 'No pain currently', labelHindi: 'वर्तमान में कोई दर्द नहीं', value: 'none', icon: '✅' },
        ],
        validation: { required: true },
        priority: 'high',
        order: 5,
      },

      {
        id: 'pain_level',
        type: 'scale',
        category: 'health_profile',
        question: 'On a scale of 1-5, how would you rate your overall pain/discomfort level?',
        questionHindi: '1-5 के पैमाने पर, आप अपने समग्र दर्द/असुविधा के स्तर को कैसे रेट करेंगे?',
        description: '1 = No pain, 5 = Severe pain',
        descriptionHindi: '1 = कोई दर्द नहीं, 5 = गंभीर दर्द',
        conditional: {
          dependsOn: 'current_pain_areas',
          condition: 'not_contains',
          value: 'none'
        },
        validation: { required: true, min: 1, max: 5 },
        priority: 'high',
        order: 6,
      },

      {
        id: 'exercise_frequency',
        type: 'single_choice',
        category: 'health_profile',
        question: 'How often do you currently exercise or engage in physical activity?',
        questionHindi: 'आप वर्तमान में कितनी बार व्यायाम या शारीरिक गतिविधि में संलग्न होते हैं?',
        options: [
          { id: 'daily', label: 'Daily', labelHindi: 'दैनिक', value: 'daily', icon: '🏃‍♂️' },
          { id: 'few_times_week', label: '3-4 times per week', labelHindi: 'सप्ताह में 3-4 बार', value: 'few_times_week', icon: '💪' },
          { id: 'weekly', label: '1-2 times per week', labelHindi: 'सप्ताह में 1-2 बार', value: 'weekly', icon: '🚶‍♂️' },
          { id: 'monthly', label: 'Few times per month', labelHindi: 'महीने में कुछ बार', value: 'monthly', icon: '🧘‍♀️' },
          { id: 'rarely', label: 'Rarely', labelHindi: 'शायद ही कभी', value: 'rarely', icon: '😴' },
          { id: 'never', label: 'Never', labelHindi: 'कभी नहीं', value: 'never', icon: '🛋️' },
        ],
        validation: { required: true },
        priority: 'high',
        order: 7,
      },

      // Goals Questions
      {
        id: 'primary_goal',
        type: 'single_choice',
        category: 'goals',
        question: 'What is your primary goal for improving your posture and wellness?',
        questionHindi: 'आपके आसन और कल्याण में सुधार के लिए आपका मुख्य लक्ष्य क्या है?',
        options: [
          { id: 'pain_relief', label: 'Pain Relief', labelHindi: 'दर्द से राहत', value: 'pain_relief', icon: '🩹' },
          { id: 'posture_improvement', label: 'Better Posture', labelHindi: 'बेहतर आसन', value: 'posture_improvement', icon: '🧍‍♂️' },
          { id: 'stress_reduction', label: 'Stress Reduction', labelHindi: 'तनाव कम करना', value: 'stress_reduction', icon: '😌' },
          { id: 'energy_boost', label: 'More Energy', labelHindi: 'अधिक ऊर्जा', value: 'energy_boost', icon: '⚡' },
          { id: 'productivity_enhancement', label: 'Better Productivity', labelHindi: 'बेहतर उत्पादकता', value: 'productivity_enhancement', icon: '📈' },
          { id: 'sleep_improvement', label: 'Better Sleep', labelHindi: 'बेहतर नींद', value: 'sleep_improvement', icon: '😴' },
          { id: 'general_wellness', label: 'Overall Wellness', labelHindi: 'समग्र कल्याण', value: 'general_wellness', icon: '🌟' },
        ],
        validation: { required: true },
        priority: 'high',
        order: 8,
      },

      {
        id: 'time_commitment',
        type: 'single_choice',
        category: 'goals',
        question: 'How much time can you realistically commit to daily wellness activities?',
        questionHindi: 'आप वास्तविक रूप से दैनिक कल्याण गतिविधियों के लिए कितना समय दे सकते हैं?',
        options: [
          { id: '5-10min', label: '5-10 minutes', labelHindi: '5-10 मिनट', value: '5-10min', icon: '⏱️' },
          { id: '10-20min', label: '10-20 minutes', labelHindi: '10-20 मिनट', value: '10-20min', icon: '⏰' },
          { id: '20-30min', label: '20-30 minutes', labelHindi: '20-30 मिनट', value: '20-30min', icon: '🕐' },
          { id: '30-60min', label: '30-60 minutes', labelHindi: '30-60 मिनट', value: '30-60min', icon: '🕑' },
          { id: '60min+', label: 'More than 1 hour', labelHindi: '1 घंटे से अधिक', value: '60min+', icon: '🕕' },
        ],
        validation: { required: true },
        priority: 'high',
        order: 9,
      },

      // Device Usage Questions
      {
        id: 'screen_time_computer',
        type: 'scale',
        category: 'device_usage',
        question: 'How many hours per day do you spend on a computer/laptop?',
        questionHindi: 'आप दिन में कितने घंटे कंप्यूटर/लैपटॉप पर बिताते हैं?',
        validation: { required: true, min: 0, max: 16 },
        priority: 'medium',
        order: 10,
      },

      {
        id: 'screen_time_phone',
        type: 'scale',
        category: 'device_usage',
        question: 'How many hours per day do you spend on your smartphone?',
        questionHindi: 'आप दिन में कितने घंटे अपने स्मार्टफोन पर बिताते हैं?',
        validation: { required: true, min: 0, max: 16 },
        priority: 'medium',
        order: 11,
      },

      // Lifestyle Questions
      {
        id: 'sleep_quality',
        type: 'scale',
        category: 'lifestyle',
        question: 'How would you rate your sleep quality? (1 = Very Poor, 5 = Excellent)',
        questionHindi: 'आप अपनी नींद की गुणवत्ता को कैसे रेट करेंगे? (1 = बहुत खराब, 5 = उत्कृष्ट)',
        validation: { required: true, min: 1, max: 5 },
        priority: 'medium',
        order: 12,
      },

      {
        id: 'stress_level',
        type: 'scale',
        category: 'work_profile',
        question: 'How would you rate your current stress level? (1 = Very Low, 5 = Very High)',
        questionHindi: 'आप अपने वर्तमान तनाव के स्तर को कैसे रेट करेंगे? (1 = बहुत कम, 5 = बहुत अधिक)',
        validation: { required: true, min: 1, max: 5 },
        priority: 'medium',
        order: 13,
      },

      // Preferences Questions
      {
        id: 'reminder_style',
        type: 'single_choice',
        category: 'preferences',
        question: 'What type of reminders would you prefer?',
        questionHindi: 'आप किस प्रकार के रिमाइंडर पसंद करेंगे?',
        options: [
          { id: 'gentle', label: 'Gentle nudges', labelHindi: 'सौम्य संकेत', value: 'gentle', icon: '🔔' },
          { id: 'firm', label: 'Firm reminders', labelHindi: 'दृढ़ रिमाइंडर', value: 'firm', icon: '⏰' },
          { id: 'motivational', label: 'Motivational messages', labelHindi: 'प्रेरणादायक संदेश', value: 'motivational', icon: '💪' },
          { id: 'educational', label: 'Educational tips', labelHindi: 'शैक्षिक सुझाव', value: 'educational', icon: '📚' },
        ],
        validation: { required: true },
        priority: 'low',
        order: 14,
      },

      {
        id: 'content_preference',
        type: 'single_choice',
        category: 'preferences',
        question: 'How do you prefer to receive exercise instructions?',
        questionHindi: 'आप व्यायाम निर्देश कैसे प्राप्त करना पसंद करते हैं?',
        options: [
          { id: 'video', label: 'Video demonstrations', labelHindi: 'वीडियो प्रदर्शन', value: 'video', icon: '📹' },
          { id: 'audio', label: 'Audio guidance', labelHindi: 'ऑडियो मार्गदर्शन', value: 'audio', icon: '🎵' },
          { id: 'text', label: 'Text instructions', labelHindi: 'टेक्स्ट निर्देश', value: 'text', icon: '📝' },
          { id: 'mixed', label: 'Mixed (Video + Audio + Text)', labelHindi: 'मिश्रित (वीडियो + ऑडियो + टेक्स्ट)', value: 'mixed', icon: '🎯' },
        ],
        validation: { required: true },
        priority: 'low',
        order: 15,
      },
    ];

    logger.info('Question bank initialized', { questionCount: this.questionBank.length }, 'QuestionnaireService');
  }

  /**
   * Start a new questionnaire session
   */
  async startSession(
    userId: string,
    type: 'onboarding' | 'periodic_update' | 'goal_reassessment' | 'health_check' = 'onboarding'
  ): Promise<QuestionnaireSession> {
    try {
      const session: QuestionnaireSession = {
        id: `session_${Date.now()}_${userId}`,
        userId,
        type,
        startedAt: new Date(),
        responses: [],
        completionPercentage: 0,
        insights: [],
        recommendations: [],
      };

      this.currentSession = session;
      
      // Cache session for offline support
      await cacheManager.set(`questionnaire_session_${userId}`, session, 24 * 60 * 60); // 24 hours
      
      logger.info('Questionnaire session started', {
        sessionId: session.id,
        userId,
        type,
      }, 'QuestionnaireService');

      return session;
    } catch (error) {
      logger.error('Failed to start questionnaire session', error, 'QuestionnaireService');
      throw error;
    }
  }

  /**
   * Get next question based on current responses and adaptive logic
   */
  getNextQuestion(responses: QuestionnaireResponse[]): QuestionnaireQuestion | null {
    const answeredQuestionIds = responses.map(r => r.questionId);
    
    // Filter questions based on conditional logic
    const availableQuestions = this.questionBank.filter(question => {
      // Skip if already answered
      if (answeredQuestionIds.includes(question.id)) {
        return false;
      }

      // Check conditional logic
      if (question.conditional) {
        const dependentResponse = responses.find(r => r.questionId === question.conditional!.dependsOn);
        if (!dependentResponse) {
          return false; // Dependent question not answered yet
        }

        const { condition, value } = question.conditional;
        const responseValue = dependentResponse.answer;

        switch (condition) {
          case 'equals':
            return responseValue === value;
          case 'not_equals':
            return responseValue !== value;
          case 'contains':
            return Array.isArray(responseValue) && responseValue.includes(value);
          case 'not_contains':
            return Array.isArray(responseValue) && !responseValue.includes(value);
          case 'greater_than':
            return Number(responseValue) > Number(value);
          case 'less_than':
            return Number(responseValue) < Number(value);
          default:
            return true;
        }
      }

      return true;
    });

    // Sort by priority and order
    availableQuestions.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return a.order - b.order;
    });

    return availableQuestions[0] || null;
  }

  /**
   * Submit response to current question
   */
  async submitResponse(
    sessionId: string,
    questionId: string,
    answer: any,
    confidence?: number
  ): Promise<void> {
    try {
      if (!this.currentSession || this.currentSession.id !== sessionId) {
        throw new Error('Invalid session');
      }

      const response: QuestionnaireResponse = {
        questionId,
        answer,
        timestamp: new Date(),
        confidence,
      };

      this.currentSession.responses.push(response);
      
      // Update completion percentage
      const totalQuestions = this.getTotalQuestionsForSession(this.currentSession.responses);
      this.currentSession.completionPercentage = Math.round(
        (this.currentSession.responses.length / totalQuestions) * 100
      );

      // Update cached session
      await cacheManager.set(
        `questionnaire_session_${this.currentSession.userId}`,
        this.currentSession,
        24 * 60 * 60
      );

      logger.debug('Response submitted', {
        sessionId,
        questionId,
        completionPercentage: this.currentSession.completionPercentage,
      }, 'QuestionnaireService');
    } catch (error) {
      logger.error('Failed to submit response', error, 'QuestionnaireService');
      throw error;
    }
  }

  /**
   * Complete questionnaire session and generate insights
   */
  async completeSession(sessionId: string): Promise<QuestionnaireSession> {
    try {
      if (!this.currentSession || this.currentSession.id !== sessionId) {
        throw new Error('Invalid session');
      }

      this.currentSession.completedAt = new Date();
      this.currentSession.completionPercentage = 100;

      // Generate insights and recommendations
      this.currentSession.insights = this.generateInsights(this.currentSession.responses);
      this.currentSession.recommendations = this.generateRecommendations(
        this.currentSession.responses,
        this.currentSession.insights
      );

      // Save to database
      await dataService.saveQuestionnaireSession(this.currentSession);
      
      // Update user preferences based on responses
      await this.updateUserPreferences(this.currentSession.userId, this.currentSession.responses);

      logger.info('Questionnaire session completed', {
        sessionId,
        insightsCount: this.currentSession.insights.length,
        recommendationsCount: this.currentSession.recommendations.length,
      }, 'QuestionnaireService');

      const completedSession = { ...this.currentSession };
      this.currentSession = null;
      
      return completedSession;
    } catch (error) {
      logger.error('Failed to complete questionnaire session', error, 'QuestionnaireService');
      throw error;
    }
  }

  /**
   * Generate insights from responses
   */
  private generateInsights(responses: QuestionnaireResponse[]): UserInsight[] {
    const insights: UserInsight[] = [];
    const responseMap = new Map(responses.map(r => [r.questionId, r.answer]));

    // Posture risk assessment
    const painAreas = responseMap.get('current_pain_areas') || [];
    const painLevel = responseMap.get('pain_level') || 0;
    const workType = responseMap.get('work_type');
    const workHours = responseMap.get('work_hours') || 0;
    const screenTimeComputer = responseMap.get('screen_time_computer') || 0;
    const exerciseFrequency = responseMap.get('exercise_frequency');

    // High posture risk insight
    if (painAreas.length > 0 && painLevel >= 3) {
      insights.push({
        id: 'high_posture_risk',
        category: 'posture_risk',
        title: 'High Posture Risk Detected',
        description: `You're experiencing ${painLevel}/5 pain in ${painAreas.length} areas. This indicates significant posture-related issues that need attention.`,
        severity: 'high',
        confidence: 0.9,
        actionable: true,
        relatedQuestions: ['current_pain_areas', 'pain_level'],
      });
    }

    // Ergonomic issues
    if (workType === 'desk_job' && workHours >= 6 && screenTimeComputer >= 6) {
      insights.push({
        id: 'ergonomic_concern',
        category: 'ergonomic_issue',
        title: 'Extended Screen Time Risk',
        description: `You spend ${workHours} hours working and ${screenTimeComputer} hours on computer daily. This prolonged screen time significantly increases posture risks.`,
        severity: 'medium',
        confidence: 0.8,
        actionable: true,
        relatedQuestions: ['work_type', 'work_hours', 'screen_time_computer'],
      });
    }

    // Exercise deficiency
    if (exerciseFrequency === 'rarely' || exerciseFrequency === 'never') {
      insights.push({
        id: 'exercise_deficiency',
        category: 'lifestyle_factor',
        title: 'Low Physical Activity',
        description: 'Your current exercise frequency is insufficient to counteract the effects of prolonged sitting and poor posture.',
        severity: 'medium',
        confidence: 0.7,
        actionable: true,
        relatedQuestions: ['exercise_frequency'],
      });
    }

    // Sleep quality impact
    const sleepQuality = responseMap.get('sleep_quality') || 0;
    if (sleepQuality <= 2 && painAreas.length > 0) {
      insights.push({
        id: 'sleep_posture_connection',
        category: 'health_concern',
        title: 'Sleep Quality & Posture Connection',
        description: 'Poor sleep quality combined with posture issues can create a cycle of discomfort and fatigue.',
        severity: 'medium',
        confidence: 0.6,
        actionable: true,
        relatedQuestions: ['sleep_quality', 'current_pain_areas'],
      });
    }

    // Stress impact
    const stressLevel = responseMap.get('stress_level') || 0;
    if (stressLevel >= 4 && painAreas.includes('neck') || painAreas.includes('shoulders')) {
      insights.push({
        id: 'stress_tension_connection',
        category: 'lifestyle_factor',
        title: 'Stress-Related Muscle Tension',
        description: 'High stress levels often manifest as neck and shoulder tension, contributing to poor posture.',
        severity: 'medium',
        confidence: 0.7,
        actionable: true,
        relatedQuestions: ['stress_level', 'current_pain_areas'],
      });
    }

    return insights;
  }

  /**
   * Generate personalized recommendations
   */
  private generateRecommendations(
    responses: QuestionnaireResponse[],
    insights: UserInsight[]
  ): PersonalizedRecommendation[] {
    const recommendations: PersonalizedRecommendation[] = [];
    const responseMap = new Map(responses.map(r => [r.questionId, r.answer]));

    const timeCommitment = responseMap.get('time_commitment');
    const primaryGoal = responseMap.get('primary_goal');
    const workType = responseMap.get('work_type');
    const painAreas = responseMap.get('current_pain_areas') || [];
    const exerciseFrequency = responseMap.get('exercise_frequency');

    // Exercise routine recommendations
    if (timeCommitment === '5-10min') {
      recommendations.push({
        id: 'quick_daily_routine',
        type: 'exercise_routine',
        title: 'Quick Daily Posture Breaks',
        description: 'A 5-minute routine of neck rolls, shoulder shrugs, and spinal twists perfect for busy schedules.',
        priority: 'high',
        estimatedImpact: 7,
        timeToImplement: 'immediate',
        category: 'Exercise',
        actionSteps: [
          'Set hourly reminders for posture breaks',
          'Practice 30-second neck stretches',
          'Do 10 shoulder rolls every hour',
          'Take 2-minute walking breaks'
        ],
        relatedInsights: insights.filter(i => i.category === 'posture_risk').map(i => i.id),
      });
    }

    // Ergonomic recommendations for desk workers
    if (workType === 'desk_job') {
      recommendations.push({
        id: 'ergonomic_setup',
        type: 'ergonomic_adjustment',
        title: 'Optimize Your Workspace',
        description: 'Simple adjustments to your desk setup can dramatically improve your posture throughout the day.',
        priority: 'high',
        estimatedImpact: 8,
        timeToImplement: 'days',
        category: 'Ergonomics',
        actionSteps: [
          'Adjust monitor to eye level',
          'Use a document holder beside your screen',
          'Position keyboard and mouse at elbow height',
          'Use a lumbar support cushion',
          'Keep feet flat on floor or footrest'
        ],
        relatedInsights: insights.filter(i => i.category === 'ergonomic_issue').map(i => i.id),
      });
    }

    // Pain-specific recommendations
    if (painAreas.includes('neck')) {
      recommendations.push({
        id: 'neck_pain_relief',
        type: 'exercise_routine',
        title: 'Neck Pain Relief Program',
        description: 'Targeted exercises to reduce neck tension and improve cervical spine alignment.',
        priority: 'high',
        estimatedImpact: 8,
        timeToImplement: 'immediate',
        category: 'Pain Relief',
        actionSteps: [
          'Practice chin tucks 10 times, 3x daily',
          'Do gentle neck side bends',
          'Use heat therapy for 15 minutes',
          'Sleep with proper pillow support'
        ],
        relatedInsights: insights.filter(i => i.title.includes('neck') || i.category === 'posture_risk').map(i => i.id),
      });
    }

    // Goal-specific recommendations
    if (primaryGoal === 'stress_reduction') {
      recommendations.push({
        id: 'stress_relief_posture',
        type: 'lifestyle_change',
        title: 'Posture-Based Stress Relief',
        description: 'Combine posture improvement with stress reduction techniques for maximum benefit.',
        priority: 'medium',
        estimatedImpact: 7,
        timeToImplement: 'weeks',
        category: 'Wellness',
        actionSteps: [
          'Practice deep breathing with good posture',
          'Try progressive muscle relaxation',
          'Use meditation apps during breaks',
          'Create a calming workspace environment'
        ],
        relatedInsights: insights.filter(i => i.category === 'lifestyle_factor').map(i => i.id),
      });
    }

    // Exercise frequency recommendations
    if (exerciseFrequency === 'rarely' || exerciseFrequency === 'never') {
      recommendations.push({
        id: 'gradual_activity_increase',
        type: 'habit_formation',
        title: 'Gradual Activity Introduction',
        description: 'Start with small, manageable activities to build a sustainable exercise habit.',
        priority: 'medium',
        estimatedImpact: 6,
        timeToImplement: 'weeks',
        category: 'Fitness',
        actionSteps: [
          'Start with 5-minute daily walks',
          'Use stairs instead of elevators',
          'Park farther from destinations',
          'Set movement reminders on your phone'
        ],
        relatedInsights: insights.filter(i => i.id === 'exercise_deficiency').map(i => i.id),
      });
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * Update user preferences based on questionnaire responses
   */
  private async updateUserPreferences(
    userId: string,
    responses: QuestionnaireResponse[]
  ): Promise<void> {
    try {
      const responseMap = new Map(responses.map(r => [r.questionId, r.answer]));
      
      // Build comprehensive user preferences
      const preferences: Partial<UserPreferences> = {
        workProfile: {
          workType: responseMap.get('work_type') || 'other',
          hoursPerDay: responseMap.get('work_hours') || 8,
          workEnvironment: responseMap.get('work_environment') || 'office',
          deskSetup: {
            hasErgonomicChair: false, // Would be determined by additional questions
            hasStandingDesk: false,
            monitorHeight: 'eye_level',
            keyboardMouse: 'standard',
          },
          breakFrequency: 'every_hour', // Default recommendation
          stressLevel: responseMap.get('stress_level') || 3,
        },
        healthProfile: {
          currentIssues: [], // Would map from pain areas
          pastInjuries: [],
          painAreas: responseMap.get('current_pain_areas') || [],
          painLevel: responseMap.get('pain_level') || 1,
          exerciseFrequency: responseMap.get('exercise_frequency') || 'rarely',
          sleepQuality: responseMap.get('sleep_quality') || 3,
          energyLevel: 3, // Default
          medicalConditions: [],
          medications: [],
        },
        goals: {
          primary: responseMap.get('primary_goal') || 'general_wellness',
          secondary: [],
          timeCommitment: responseMap.get('time_commitment') || '10-20min',
          preferredTime: 'flexible',
          motivationFactors: [],
          targetAchievements: [],
        },
        deviceUsage: {
          primaryDevices: ['laptop', 'smartphone'],
          screenTime: {
            computer: responseMap.get('screen_time_computer') || 0,
            phone: responseMap.get('screen_time_phone') || 0,
            tablet: 0,
            tv: 0,
          },
          usagePatterns: {
            longestContinuousUse: 4,
            averageBreakDuration: 5,
            eyeStrainFrequency: 'sometimes',
          },
        },
        personalizedSettings: {
          reminderStyle: responseMap.get('reminder_style') || 'gentle',
          feedbackPreference: 'summary',
          difficultyProgression: 'moderate',
          contentPreference: responseMap.get('content_preference') || 'mixed',
          privacyLevel: 'standard',
          dataSharing: {
            analytics: true,
            research: false,
            marketing: false,
          },
        },
      };

      // Update user preferences in database
      await dataService.updateUserPreferences(userId, preferences);
      
      logger.info('User preferences updated from questionnaire', {
        userId,
        responseCount: responses.length,
      }, 'QuestionnaireService');
    } catch (error) {
      logger.error('Failed to update user preferences', error, 'QuestionnaireService');
      throw error;
    }
  }

  /**
   * Get total questions that would be asked in this session
   */
  private getTotalQuestionsForSession(responses: QuestionnaireResponse[]): number {
    // This is a simplified calculation - in reality, it would be more complex
    // based on conditional logic and user responses
    const baseQuestions = this.questionBank.filter(q => q.priority === 'high').length;
    const conditionalQuestions = Math.floor(this.questionBank.length * 0.3); // Estimate
    return baseQuestions + conditionalQuestions;
  }

  /**
   * Get questionnaire progress for user
   */
  async getProgress(userId: string): Promise<{ completed: boolean; percentage: number; lastSession?: Date }> {
    try {
      const cachedSession = await cacheManager.get<QuestionnaireSession>(`questionnaire_session_${userId}`);
      
      if (cachedSession && !cachedSession.completedAt) {
        return {
          completed: false,
          percentage: cachedSession.completionPercentage,
          lastSession: cachedSession.startedAt,
        };
      }

      // Check database for completed sessions
      const lastSession = await dataService.getLastQuestionnaireSession(userId);
      
      return {
        completed: !!lastSession?.completedAt,
        percentage: lastSession?.completionPercentage || 0,
        lastSession: lastSession?.completedAt,
      };
    } catch (error) {
      logger.error('Failed to get questionnaire progress', error, 'QuestionnaireService');
      return { completed: false, percentage: 0 };
    }
  }

  /**
   * Resume incomplete session
   */
  async resumeSession(userId: string): Promise<QuestionnaireSession | null> {
    try {
      const cachedSession = await cacheManager.get<QuestionnaireSession>(`questionnaire_session_${userId}`);
      
      if (cachedSession && !cachedSession.completedAt) {
        this.currentSession = cachedSession;
        return cachedSession;
      }

      return null;
    } catch (error) {
      logger.error('Failed to resume questionnaire session', error, 'QuestionnaireService');
      return null;
    }
  }
}

export const questionnaireService = QuestionnaireService.getInstance();