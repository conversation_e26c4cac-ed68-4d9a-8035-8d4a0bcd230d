/**
 * Production-ready logging utility with multiple levels, structured logging,
 * and integration with monitoring services
 */

import { AppStorage, STORAGE_KEYS } from './storage';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4,
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
  userId?: string;
  sessionId?: string;
  component?: string;
  action?: string;
  duration?: number;
  error?: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };
  metadata?: {
    platform?: string;
    version?: string;
    buildNumber?: string;
    deviceId?: string;
    networkType?: string;
  };
}

export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableRemote: boolean;
  enableLocalStorage: boolean;
  maxLocalEntries: number;
  remoteEndpoint?: string;
  apiKey?: string;
  batchSize: number;
  flushInterval: number;
}

class Logger {
  private config: LoggerConfig;
  private logBuffer: LogEntry[] = [];
  private sessionId: string;
  private userId?: string;
  private flushTimer?: NodeJS.Timeout;
  private metadata: LogEntry['metadata'];

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: LogLevel.INFO,
      enableConsole: __DEV__,
      enableRemote: !__DEV__,
      enableLocalStorage: true,
      maxLocalEntries: 1000,
      batchSize: 50,
      flushInterval: 30000, // 30 seconds
      ...config,
    };

    this.sessionId = this.generateSessionId();
    this.metadata = this.collectMetadata();
    this.startFlushTimer();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private collectMetadata(): LogEntry['metadata'] {
    try {
      return {
        platform: 'react-native',
        version: '1.0.0', // Should come from app config
        buildNumber: '1', // Should come from app config
        // deviceId: DeviceInfo.getUniqueId(), // Uncomment when DeviceInfo is available
        // networkType: NetInfo.getConnectionInfo(), // Uncomment when NetInfo is available
      };
    } catch (error) {
      return {};
    }
  }

  private startFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);
  }

  setUserId(userId: string): void {
    this.userId = userId;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.config.level;
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    data?: any,
    component?: string,
    action?: string,
    duration?: number,
    error?: Error
  ): LogEntry {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      sessionId: this.sessionId,
      metadata: this.metadata,
    };

    if (this.userId) entry.userId = this.userId;
    if (data) entry.data = this.sanitizeData(data);
    if (component) entry.component = component;
    if (action) entry.action = action;
    if (duration !== undefined) entry.duration = duration;
    if (error) {
      entry.error = {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: (error as any).code,
      };
    }

    return entry;
  }

  private sanitizeData(data: any): any {
    if (!data) return data;

    try {
      // Remove sensitive information
      const sensitiveKeys = ['password', 'token', 'apiKey', 'secret', 'auth', 'credential'];
      const sanitized = JSON.parse(JSON.stringify(data));

      const sanitizeObject = (obj: any): any => {
        if (typeof obj !== 'object' || obj === null) return obj;

        if (Array.isArray(obj)) {
          return obj.map(sanitizeObject);
        }

        const result: any = {};
        for (const [key, value] of Object.entries(obj)) {
          const lowerKey = key.toLowerCase();
          if (sensitiveKeys.some(sensitive => lowerKey.includes(sensitive))) {
            result[key] = '[REDACTED]';
          } else {
            result[key] = sanitizeObject(value);
          }
        }
        return result;
      };

      return sanitizeObject(sanitized);
    } catch (error) {
      return '[SERIALIZATION_ERROR]';
    }
  }

  private writeToConsole(entry: LogEntry): void {
    if (!this.config.enableConsole) return;

    const levelNames = ['DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL'];
    const levelName = levelNames[entry.level];
    const prefix = `[${entry.timestamp}] [${levelName}] [${entry.sessionId}]`;

    const logData = {
      message: entry.message,
      ...(entry.component && { component: entry.component }),
      ...(entry.action && { action: entry.action }),
      ...(entry.duration !== undefined && { duration: `${entry.duration}ms` }),
      ...(entry.data && { data: entry.data }),
      ...(entry.error && { error: entry.error }),
    };

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(prefix, logData);
        break;
      case LogLevel.INFO:
        console.info(prefix, logData);
        break;
      case LogLevel.WARN:
        console.warn(prefix, logData);
        break;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(prefix, logData);
        break;
    }
  }

  private async writeToLocalStorage(entry: LogEntry): Promise<void> {
    if (!this.config.enableLocalStorage) return;

    try {
      const logs = await AppStorage.getObject<LogEntry[]>(STORAGE_KEYS.APP_LOGS) || [];
      
      logs.push(entry);
      
      // Keep only the most recent entries
      if (logs.length > this.config.maxLocalEntries) {
        logs.splice(0, logs.length - this.config.maxLocalEntries);
      }
      
      await AppStorage.setObject(STORAGE_KEYS.APP_LOGS, logs);
    } catch (error) {
      console.warn('Failed to write log to storage:', error);
    }
  }

  private addToBuffer(entry: LogEntry): void {
    this.logBuffer.push(entry);
    
    if (this.logBuffer.length >= this.config.batchSize) {
      this.flush();
    }
  }

  private async flush(): Promise<void> {
    if (this.logBuffer.length === 0 || !this.config.enableRemote) return;

    const logsToSend = [...this.logBuffer];
    this.logBuffer = [];

    try {
      if (this.config.remoteEndpoint && this.config.apiKey) {
        await fetch(this.config.remoteEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.config.apiKey}`,
          },
          body: JSON.stringify({
            logs: logsToSend,
            sessionId: this.sessionId,
            timestamp: new Date().toISOString(),
          }),
        });
      }
    } catch (error) {
      console.warn('Failed to send logs to remote endpoint:', error);
      // Re-add logs to buffer for retry
      this.logBuffer.unshift(...logsToSend);
    }
  }

  private log(
    level: LogLevel,
    message: string,
    data?: any,
    component?: string,
    action?: string,
    duration?: number,
    error?: Error
  ): void {
    if (!this.shouldLog(level)) return;

    const entry = this.createLogEntry(level, message, data, component, action, duration, error);
    
    this.writeToConsole(entry);
    // Fire and forget - don't block logging for async storage
    this.writeToLocalStorage(entry).catch(error => {
      console.warn('Failed to write log to storage:', error);
    });
    this.addToBuffer(entry);
  }

  // Public logging methods
  debug(message: string, data?: any, component?: string): void {
    this.log(LogLevel.DEBUG, message, data, component);
  }

  info(message: string, data?: any, component?: string): void {
    this.log(LogLevel.INFO, message, data, component);
  }

  warn(message: string, data?: any, component?: string): void {
    this.log(LogLevel.WARN, message, data, component);
  }

  error(message: string, error?: Error | any, component?: string): void {
    if (error instanceof Error) {
      this.log(LogLevel.ERROR, message, undefined, component, undefined, undefined, error);
    } else {
      this.log(LogLevel.ERROR, message, error, component);
    }
  }

  fatal(message: string, error?: Error | any, component?: string): void {
    if (error instanceof Error) {
      this.log(LogLevel.FATAL, message, undefined, component, undefined, undefined, error);
    } else {
      this.log(LogLevel.FATAL, message, error, component);
    }
  }

  // Performance logging
  startTimer(action: string, component?: string): () => void {
    const startTime = Date.now();
    return () => {
      const duration = Date.now() - startTime;
      this.log(LogLevel.INFO, `Action completed: ${action}`, undefined, component, action, duration);
    };
  }

  // User action tracking
  trackUserAction(action: string, data?: any, component?: string): void {
    this.log(LogLevel.INFO, `User action: ${action}`, data, component, action);
  }

  // API call logging
  trackApiCall(endpoint: string, method: string, duration: number, status: number, error?: Error): void {
    const message = `API ${method} ${endpoint} - ${status} (${duration}ms)`;
    const data = { endpoint, method, status, duration };
    
    if (error || status >= 400) {
      this.log(LogLevel.ERROR, message, data, 'API', `${method} ${endpoint}`, duration, error);
    } else {
      this.log(LogLevel.INFO, message, data, 'API', `${method} ${endpoint}`, duration);
    }
  }

  // Get logs for debugging
  async getLocalLogs(): Promise<LogEntry[]> {
    try {
      return await AppStorage.getObject<LogEntry[]>(STORAGE_KEYS.APP_LOGS) || [];
    } catch (error) {
      console.warn('Failed to retrieve local logs:', error);
      return [];
    }
  }

  // Clear local logs
  async clearLocalLogs(): Promise<void> {
    try {
      await AppStorage.removeItem(STORAGE_KEYS.APP_LOGS);
    } catch (error) {
      console.warn('Failed to clear local logs:', error);
    }
  }

  // Export logs for support
  exportLogs(): string {
    const logs = this.getLocalLogs();
    return JSON.stringify(logs, null, 2);
  }

  // Cleanup
  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flush(); // Final flush
  }
}

// Create and export singleton logger instance
export const logger = new Logger({
  level: __DEV__ ? LogLevel.DEBUG : LogLevel.INFO,
  enableConsole: true,
  enableRemote: !__DEV__,
  enableLocalStorage: true,
  maxLocalEntries: 1000,
  batchSize: 50,
  flushInterval: 30000,
  // remoteEndpoint: 'https://your-logging-service.com/api/logs',
  // apiKey: 'your-api-key',
});

// Global error handler
if (typeof ErrorUtils !== 'undefined') {
  const originalHandler = ErrorUtils.getGlobalHandler();
  
  ErrorUtils.setGlobalHandler((error: Error, isFatal?: boolean) => {
    logger.fatal('Unhandled error', error, 'GlobalErrorHandler');
    
    if (originalHandler) {
      originalHandler(error, isFatal);
    }
  });
}

// Promise rejection handler
if (typeof global !== 'undefined' && (global as any).HermesInternal) {
  (global as any).addEventListener?.('unhandledrejection', (event: any) => {
    logger.error('Unhandled promise rejection', event.reason, 'PromiseRejectionHandler');
  });
}