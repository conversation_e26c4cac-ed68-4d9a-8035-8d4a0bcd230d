-- Create profiles table
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'premium', 'family')),
    onboarding_completed BOOLEAN DEFAULT FALSE,
    questionnaire_completed BOOLEAN DEFAULT FALSE,
    last_questionnaire_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create posture_sessions table
CREATE TABLE IF NOT EXISTS public.posture_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    session_data JSONB NOT NULL,
    duration INTEGER NOT NULL, -- Duration in seconds
    score DECIMAL(5,2), -- Posture score (0-100)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create posture_analyses table for detailed analysis storage
CREATE TABLE IF NOT EXISTS public.posture_analyses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    overall_score INTEGER NOT NULL CHECK (overall_score >= 0 AND overall_score <= 100),
    neck_angle DECIMAL(5,2) NOT NULL,
    shoulder_alignment INTEGER NOT NULL CHECK (shoulder_alignment >= 0 AND shoulder_alignment <= 100),
    spine_alignment INTEGER NOT NULL CHECK (spine_alignment >= 0 AND spine_alignment <= 100),
    hip_alignment INTEGER NOT NULL CHECK (hip_alignment >= 0 AND hip_alignment <= 100),
    recommendations TEXT[] NOT NULL DEFAULT '{}',
    session_duration INTEGER NOT NULL DEFAULT 0, -- Duration in seconds
    landmarks_data JSONB, -- Store pose landmarks if needed
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.posture_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.posture_analyses ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist and recreate them
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;

-- Create policies for profiles table
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Drop existing posture_sessions policies if they exist
DROP POLICY IF EXISTS "Users can view own sessions" ON public.posture_sessions;
DROP POLICY IF EXISTS "Users can insert own sessions" ON public.posture_sessions;
DROP POLICY IF EXISTS "Users can update own sessions" ON public.posture_sessions;
DROP POLICY IF EXISTS "Users can delete own sessions" ON public.posture_sessions;

-- Create policies for posture_sessions table
CREATE POLICY "Users can view own sessions" ON public.posture_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own sessions" ON public.posture_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own sessions" ON public.posture_sessions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own sessions" ON public.posture_sessions
    FOR DELETE USING (auth.uid() = user_id);

-- Create policies for posture_analyses table
CREATE POLICY "Users can view own analyses" ON public.posture_analyses
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own analyses" ON public.posture_analyses
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own analyses" ON public.posture_analyses
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own analyses" ON public.posture_analyses
    FOR DELETE USING (auth.uid() = user_id);

-- Enable RLS for new tables
ALTER TABLE public.questionnaire_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.questionnaire_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.personalized_recommendations ENABLE ROW LEVEL SECURITY;

-- Create policies for questionnaire_sessions table
CREATE POLICY "Users can view own questionnaire sessions" ON public.questionnaire_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own questionnaire sessions" ON public.questionnaire_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own questionnaire sessions" ON public.questionnaire_sessions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own questionnaire sessions" ON public.questionnaire_sessions
    FOR DELETE USING (auth.uid() = user_id);

-- Create policies for questionnaire_responses table
CREATE POLICY "Users can view own questionnaire responses" ON public.questionnaire_responses
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own questionnaire responses" ON public.questionnaire_responses
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own questionnaire responses" ON public.questionnaire_responses
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own questionnaire responses" ON public.questionnaire_responses
    FOR DELETE USING (auth.uid() = user_id);

-- Create policies for user_preferences table
CREATE POLICY "Users can view own preferences" ON public.user_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own preferences" ON public.user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own preferences" ON public.user_preferences
    FOR UPDATE USING (auth.uid() = user_id);

-- Create policies for user_insights table
CREATE POLICY "Users can view own insights" ON public.user_insights
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own insights" ON public.user_insights
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own insights" ON public.user_insights
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own insights" ON public.user_insights
    FOR DELETE USING (auth.uid() = user_id);

-- Create policies for personalized_recommendations table
CREATE POLICY "Users can view own recommendations" ON public.personalized_recommendations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own recommendations" ON public.personalized_recommendations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own recommendations" ON public.personalized_recommendations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own recommendations" ON public.personalized_recommendations
    FOR DELETE USING (auth.uid() = user_id);

-- Create questionnaire_sessions table
CREATE TABLE IF NOT EXISTS public.questionnaire_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    session_type TEXT NOT NULL CHECK (session_type IN ('onboarding', 'periodic_update', 'goal_reassessment', 'health_check')),
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    total_questions INTEGER DEFAULT 0,
    answered_questions INTEGER DEFAULT 0,
    session_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create questionnaire_responses table
CREATE TABLE IF NOT EXISTS public.questionnaire_responses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id UUID REFERENCES public.questionnaire_sessions(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    question_id TEXT NOT NULL,
    question_category TEXT NOT NULL,
    question_text TEXT NOT NULL,
    answer_value TEXT,
    answer_data JSONB DEFAULT '{}',
    confidence_level INTEGER DEFAULT 5 CHECK (confidence_level >= 1 AND confidence_level <= 10),
    response_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_preferences table
CREATE TABLE IF NOT EXISTS public.user_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL UNIQUE,
    language TEXT DEFAULT 'en' CHECK (language IN ('en', 'hi')),
    notifications_enabled BOOLEAN DEFAULT TRUE,
    reminder_frequency TEXT DEFAULT 'daily' CHECK (reminder_frequency IN ('never', 'daily', 'weekly', 'custom')),
    preferred_exercise_time TEXT DEFAULT 'morning' CHECK (preferred_exercise_time IN ('morning', 'afternoon', 'evening', 'flexible')),
    fitness_level TEXT DEFAULT 'beginner' CHECK (fitness_level IN ('beginner', 'intermediate', 'advanced')),
    available_time_minutes INTEGER DEFAULT 15 CHECK (available_time_minutes >= 5 AND available_time_minutes <= 120),
    focus_areas TEXT[] DEFAULT '{}',
    pain_areas TEXT[] DEFAULT '{}',
    work_environment TEXT DEFAULT 'office' CHECK (work_environment IN ('office', 'home', 'hybrid', 'standing', 'mobile')),
    daily_screen_time_hours INTEGER DEFAULT 8 CHECK (daily_screen_time_hours >= 0 AND daily_screen_time_hours <= 24),
    exercise_preferences TEXT[] DEFAULT '{}',
    health_conditions TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_insights table
CREATE TABLE IF NOT EXISTS public.user_insights (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    session_id UUID REFERENCES public.questionnaire_sessions(id) ON DELETE CASCADE,
    insight_category TEXT NOT NULL,
    insight_type TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    severity TEXT DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high')),
    priority INTEGER DEFAULT 5 CHECK (priority >= 1 AND priority <= 10),
    actionable BOOLEAN DEFAULT TRUE,
    data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create personalized_recommendations table
CREATE TABLE IF NOT EXISTS public.personalized_recommendations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    session_id UUID REFERENCES public.questionnaire_sessions(id) ON DELETE CASCADE,
    recommendation_type TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    priority INTEGER DEFAULT 5 CHECK (priority >= 1 AND priority <= 10),
    estimated_time_minutes INTEGER,
    difficulty_level TEXT DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    category TEXT NOT NULL,
    tags TEXT[] DEFAULT '{}',
    completion_status TEXT DEFAULT 'pending' CHECK (completion_status IN ('pending', 'in_progress', 'completed', 'skipped')),
    data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_posture_sessions_user_id ON public.posture_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_posture_sessions_created_at ON public.posture_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_posture_analyses_user_id ON public.posture_analyses(user_id);
CREATE INDEX IF NOT EXISTS idx_posture_analyses_created_at ON public.posture_analyses(created_at);
CREATE INDEX IF NOT EXISTS idx_posture_analyses_overall_score ON public.posture_analyses(overall_score);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
CREATE INDEX IF NOT EXISTS idx_questionnaire_sessions_user_id ON public.questionnaire_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_sessions_type ON public.questionnaire_sessions(session_type);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_session_id ON public.questionnaire_responses(session_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_user_id ON public.questionnaire_responses(user_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON public.user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_insights_user_id ON public.user_insights(user_id);
CREATE INDEX IF NOT EXISTS idx_user_insights_category ON public.user_insights(insight_category);
CREATE INDEX IF NOT EXISTS idx_personalized_recommendations_user_id ON public.personalized_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_personalized_recommendations_type ON public.personalized_recommendations(recommendation_type);

-- Create storage buckets (only if they don't exist)
INSERT INTO storage.buckets (id, name, public) 
SELECT 'avatars', 'avatars', true
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'avatars');

INSERT INTO storage.buckets (id, name, public) 
SELECT 'session-data', 'session-data', false
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'session-data');

-- Drop existing storage policies if they exist
DROP POLICY IF EXISTS "Avatar images are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Users can access their own session data" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own session data" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own session data" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own session data" ON storage.objects;

-- Create storage policies
CREATE POLICY "Avatar images are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Users can upload their own avatar" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'avatars' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can update their own avatar" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'avatars' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their own avatar" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'avatars' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Session data storage policies
CREATE POLICY "Users can access their own session data" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'session-data' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can upload their own session data" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'session-data' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can update their own session data" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'session-data' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their own session data" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'session-data' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Function to automatically create profile and preferences on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Create profile
    INSERT INTO public.profiles (id, email, full_name, onboarding_completed, questionnaire_completed)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'full_name',
        FALSE,
        FALSE
    );

    -- Create default user preferences
    INSERT INTO public.user_preferences (user_id)
    VALUES (NEW.id);

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile on signup
CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at on profiles
CREATE OR REPLACE TRIGGER on_profiles_updated
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Trigger to update updated_at on posture_analyses
CREATE OR REPLACE TRIGGER on_posture_analyses_updated
    BEFORE UPDATE ON public.posture_analyses
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Triggers for new tables
CREATE OR REPLACE TRIGGER on_questionnaire_sessions_updated
    BEFORE UPDATE ON public.questionnaire_sessions
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE OR REPLACE TRIGGER on_user_preferences_updated
    BEFORE UPDATE ON public.user_preferences
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE OR REPLACE TRIGGER on_user_insights_updated
    BEFORE UPDATE ON public.user_insights
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE OR REPLACE TRIGGER on_personalized_recommendations_updated
    BEFORE UPDATE ON public.personalized_recommendations
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();